using Vsp.Infrastructure.Interface;
using Vsp.PayrollConfiguration.Repository.Converters;

namespace Vsp.PayrollConfiguration.Repository;

public class LoketContext : DbContextBase, ILoketContext
{
    public DbContext DbContext => this;

    #region Base

    public DbSet<InheritanceLevel> InheritanceLevels { get; set; }
    public DbSet<Year> Years { get; set; }
    public DbSet<PayrollPeriod> PayrollPeriods { get; set; }

    #endregion

    #region Loket

    public DbSet<Provider> Providers { get; set; }
    public DbSet<Employer> Employers { get; set; }
    public DbSet<Administration> Administrations { get; set; }

    public DbSet<Team> Teams { get; set; }
    public DbSet<User> Users { get; set; }

    #endregion

    public LoketContext(ILoketConnectionStringFactory connectionStringFactory)
        : base(new DbContextOptionsBuilder<LoketContext>().UseSqlServer(connectionStringFactory.GetConnectionString()).Options)
    { }

    public LoketContext(DbContextOptions options) : base(options)
    { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("Loket");

        modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder) =>
        configurationBuilder
            .Properties<DateOnly>()
            .HaveConversion<DateOnlyToDateTimeConverter>();
}
