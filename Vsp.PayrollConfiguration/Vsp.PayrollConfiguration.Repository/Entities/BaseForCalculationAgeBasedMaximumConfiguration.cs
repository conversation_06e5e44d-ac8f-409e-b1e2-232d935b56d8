using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class BaseForCalculationAgeBasedMaximumConfiguration : GeneratedIdEntityTypeConfigurationBase<BaseForCalculationAgeBasedMaximum>
{
    public override void Configure(EntityTypeBuilder<BaseForCalculationAgeBasedMaximum> builder)
    {
        base.Configure(builder);

        builder.ToTable("GrondslagMaximum", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.Age, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.Age).HasColumnName("GrondslagMaximumID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");
        builder.Property(x => x.Maximum).HasColumnName("Bedrag");

        // DefinedAtLevel properties
        builder.Property(x => x.AgeDefinedAtLevel).HasColumnName("GrondslagMaximumIDAttribuut");
        builder.Property(x => x.MaximumDefinedAtLevel).HasColumnName("BedragAttribuut");

        // Relationships
        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
    }
}
