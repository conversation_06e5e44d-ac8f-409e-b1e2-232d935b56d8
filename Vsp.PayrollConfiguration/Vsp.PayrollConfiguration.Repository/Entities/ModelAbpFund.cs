namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelAbpFund : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int AbpFundId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; }
    
    public decimal? TotalContribution { get; set; }
    public decimal? EmploymentContribution { get; set; }
    public decimal? Franchise { get; set; }
    public decimal? FranchiseUpToAge40 { get; set; }
    public decimal? FranchiseUpToAge50 { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
    // TODO: navigational properties if needed
}
