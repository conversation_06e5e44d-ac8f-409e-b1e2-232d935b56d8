namespace Vsp.PayrollConfiguration.Repository.Entities;

public class UnitPercentage : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ComponentId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; }

    public int ComponentIdDefinedAtLevel { get; set; }

    public decimal Percentage { get; set; }
    public int PercentageDefinedAtLevel { get; set; }

    public int CalculateOver { get; set; }
    public int CalculateOverDefinedAtLevel { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public Component Component { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
    public CtCalculateOver CtCalculateOver { get; set; } = null!;

    public ModelComponent? ModelComponent { get; set; }
}