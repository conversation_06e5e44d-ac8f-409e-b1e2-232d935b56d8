namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class CalculationRuleConfiguration : GeneratedIdEntityTypeConfigurationBase<CalculationRule>
{
    public override void Configure(EntityTypeBuilder<CalculationRule> builder)
    {
        base.Configure(builder);

        builder.ToTable("RekenRegel", "Ulsa");
        builder.<PERSON>Key(x => new { x.InheritanceLevelId, x.YearId, x.CalculationRuleId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.CalculationRuleId).HasColumnName("RekenRegelID");
        builder.Property(x => x.ResultComponentId).HasColumnName("ResultaatComponentID");

        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ResultComponentId });
    }
}