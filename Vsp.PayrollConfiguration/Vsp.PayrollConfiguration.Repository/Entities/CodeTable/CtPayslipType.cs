namespace Vsp.PayrollConfiguration.Repository.Entities.CodeTable;

// NOTE: For audit trail: Because they key column is UlsaCode (not standard Code) - this cannot be a "normal" code table!
public class CtPayslipType : CodeTable, IMutableCodeTable
{
    // Explicitly implement the IMutableCodeTable interface to resolve nullability mismatch
    string IMutableCodeTable.Omschrijving
    {
        get => this.Omschrijving ?? string.Empty; // Provide a default value to ensure non-nullability
        set => this.Omschrijving = value;
    }
}