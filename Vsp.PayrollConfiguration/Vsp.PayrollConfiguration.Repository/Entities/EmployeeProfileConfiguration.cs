namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class EmployeeProfileConfiguration : GeneratedIdEntityTypeConfigurationBase<EmployeeProfile>
{
    public override void Configure(EntityTypeBuilder<EmployeeProfile> builder)
    {
        base.Configure(builder);

        builder.ToTable("WerknemerProfiel", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.EmployeeProfileId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.EmployeeProfileId).HasColumnName("WerknemerProfielID");
        builder.Property(x => x.Omschrijving).HasColumnName("Omschrijving").HasMaxLength(50);
        builder.Property(x => x.EmployeeProfileIdDefinedAtLevel).HasColumnName("WerknemerProfielIDAttribuut");
        builder.Property(x => x.DescriptionDefinedAtLevel).HasColumnName("OmschrijvingAttribuut");

        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });

    }
}
