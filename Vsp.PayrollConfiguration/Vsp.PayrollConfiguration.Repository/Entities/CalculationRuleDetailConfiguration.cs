namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class CalculationRuleDetailConfiguration : GeneratedIdEntityTypeConfigurationBase<CalculationRuleDetail>
{
    public override void Configure(EntityTypeBuilder<CalculationRuleDetail> builder)
    {
        base.Configure(builder);

        builder.ToTable("RekenRegelDetail", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.CalculationRuleId, x.CalculationRuleDetailId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.CalculationRuleId).HasColumnName("RekenRegelID");
        builder.Property(x => x.CalculationRuleDetailId).HasColumnName("RekenRegelDetailID");
        builder.Property(x => x.Element).HasColumnName("Element");

        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.Element });
    }
}