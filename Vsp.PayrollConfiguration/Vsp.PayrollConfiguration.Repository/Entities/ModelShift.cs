namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelShift : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ShiftId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; }

    public decimal? FullTimeHoursPerWeek { get; set; }
    public decimal? BonusPercentage { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public VwPayrollPeriod VwPayrollPeriod { get; set; } = null!;
}
