namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class BaseForCalculationConfiguration : GeneratedIdEntityTypeConfigurationBase<BaseForCalculation>
{
    public override void Configure(EntityTypeBuilder<BaseForCalculation> builder)
    {
        base.Configure(builder);

        builder.ToTable("Grondslag", "Ulsa");
        builder.<PERSON><PERSON>ey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.Description).HasColumnName("Omschrijving").HasMaxLength(50);
        builder.Property(x => x.BaseType).HasColumnName("SoortGrondslagLoonaangifte");
        builder.Property(x => x.StartEmployeeAgeType).HasColumnName("IngangsMoment");
        builder.Property(x => x.StartEmployeeAge).HasColumnName("IngangsMomentEenheden");
        builder.Property(x => x.EndEmployeeAgeType).HasColumnName("EindMoment");
        builder.Property(x => x.EndEmployeeAge).HasColumnName("EindMomentEenheden");
        builder.Property(x => x.ResultPayrollComponentId).HasColumnName("ResultaatComponentID");
        builder.Property(x => x.Percentage).HasColumnName("Percentage");
        builder.Property(x => x.CalculationPayrollPeriodNumber).HasColumnName("PeriodeBerekenen");
        builder.Property(x => x.ReferencePayrollPeriodNumber).HasColumnName("PeriodePeil");
        builder.Property(x => x.PayoutPayrollPeriodNumber).HasColumnName("PeriodeUitbetaling");
        builder.Property(x => x.AccrualEndPayrollPeriodNumber).HasColumnName("PeriodeEindeOpbouw");
        builder.Property(x => x.PayslipType).HasColumnName("Strook");
        builder.Property(x => x.IsPayoutAtStartOfEmployment).HasColumnName("VooruitbetalingBijInDienst");
        builder.Property(x => x.IsPayoutAtEndOfEmployment).HasColumnName("UitbetalingBijUitDienst");
        builder.Property(x => x.AdvancePayrollComponentId).HasColumnName("ReserveringsComponentID");
        builder.Property(x => x.AdvancePercentage).HasColumnName("ReserveringsPercentage");
        builder.Property(x => x.AdvancePayrollPeriodNumber).HasColumnName("PeriodeVoorschot");
        builder.Property(x => x.PeriodicReservationPayrollComponentId).HasColumnName("PeriodiekeReserveringsComponentID");
        builder.Property(x => x.FinancialReservationPercentage).HasColumnName("ReserveringsPercentageFinancieel");
        builder.Property(x => x.FinancialMarkupPercentage).HasColumnName("OpslagPercentage");
        builder.Property(x => x.IsCumulativeCalculation).HasColumnName("CumulatieveGrondslag");
        builder.Property(x => x.IsPartTimeCalculation).HasColumnName("BerekeningViaDeeltijd");
        builder.Property(x => x.IsAutomaticCalculation).HasColumnName("AutomatischBerekenen");
        builder.Property(x => x.IsSupplementingDailyWage).HasColumnName("DagloonSuppleren");
        builder.Property(x => x.MinimumMaximumType).HasColumnName("SoortGrondslagMinimumMaximum");

        // DefinedAtLevel properties
        builder.Property(x => x.BaseForCalculationIdDefinedAtLevel).HasColumnName("GrondslagIDAttribuut");
        builder.Property(x => x.DescriptionDefinedAtLevel).HasColumnName("OmschrijvingAttribuut");
        builder.Property(x => x.BaseTypeDefinedAtLevel).HasColumnName("SoortGrondslagLoonaangifteAttribuut");
        builder.Property(x => x.StartEmployeeAgeTypeDefinedAtLevel).HasColumnName("IngangsMomentAttribuut");
        builder.Property(x => x.StartEmployeeAgeDefinedAtLevel).HasColumnName("IngangsMomentEenhedenAttribuut");
        builder.Property(x => x.EndEmployeeAgeTypeDefinedAtLevel).HasColumnName("EindMomentAttribuut");
        builder.Property(x => x.EndEmployeeAgeDefinedAtLevel).HasColumnName("EindMomentEenhedenAttribuut");
        builder.Property(x => x.ResultPayrollComponentDefinedAtLevel).HasColumnName("ResultaatComponentIDAttribuut");
        builder.Property(x => x.PercentageDefinedAtLevel).HasColumnName("PercentageAttribuut");
        builder.Property(x => x.CalculationPayrollPeriodDefinedAtLevel).HasColumnName("PeriodeBerekenenAttribuut");
        builder.Property(x => x.ReferencePayrollPeriodDefinedAtLevel).HasColumnName("PeriodePeilAttribuut");
        builder.Property(x => x.PayoutPayrollPeriodDefinedAtLevel).HasColumnName("PeriodeUitbetalingAttribuut");
        builder.Property(x => x.AccrualEndPayrollPeriodDefinedAtLevel).HasColumnName("PeriodeEindeOpbouwAttribuut");
        builder.Property(x => x.PayslipTypeDefinedAtLevel).HasColumnName("StrookAttribuut");
        builder.Property(x => x.IsPayoutAtStartOfEmploymentDefinedAtLevel).HasColumnName("VooruitbetalingBijInDienstAttribuut");
        builder.Property(x => x.IsPayoutAtEndOfEmploymentDefinedAtLevel).HasColumnName("UitbetalingBijUitDienstAttribuut");
        builder.Property(x => x.AdvancePayrollComponentDefinedAtLevel).HasColumnName("ReserveringsComponentIDAttribuut");
        builder.Property(x => x.AdvancePercentageDefinedAtLevel).HasColumnName("ReserveringsPercentageAttribuut");
        builder.Property(x => x.AdvancePayrollPeriodNumberDefinedAtLevel).HasColumnName("PeriodeVoorschotAttribuut");
        builder.Property(x => x.PeriodicReservationPayrollComponentDefinedAtLevel).HasColumnName("PeriodiekeReserveringsComponentIDAttribuut");
        builder.Property(x => x.FinancialReservationPercentageDefinedAtLevel).HasColumnName("ReserveringsPercentageFinancieelAttribuut");
        builder.Property(x => x.FinancialMarkupPercentageDefinedAtLevel).HasColumnName("OpslagPercentageAttribuut");
        builder.Property(x => x.IsCumulativeCalculationDefinedAtLevel).HasColumnName("CumulatieveGrondslagAttribuut");
        builder.Property(x => x.IsPartTimeCalculationDefinedAtLevel).HasColumnName("BerekeningViaDeeltijdAttribuut");
        builder.Property(x => x.IsAutomaticCalculationDefinedAtLevel).HasColumnName("AutomatischBerekenenAttribuut");
        builder.Property(x => x.IsSupplementingDailyWageDefinedAtLevel).HasColumnName("DagloonSupplerenAttribuut");
        builder.Property(x => x.MinimumMaximumTypeDefinedAtLevel).HasColumnName("SoortGrondslagMinimumMaximumAttribuut");

        // Misc Relationships
        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.ResultPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ResultPayrollComponentId });
        builder.HasOne(x => x.AdvancePayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, ReservationPayrollComponentId = x.AdvancePayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.PeriodicReservationPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PeriodicReservationPayrollComponentId }).IsRequired(false);

        // Code table relationships
        builder.HasOne(x => x.CtBaseType).WithMany().HasForeignKey(x => x.BaseType).IsRequired(false);
        builder.HasOne(x => x.CtStartEmployeeAgeType).WithMany().HasForeignKey(x => x.StartEmployeeAgeType).IsRequired(false);
        builder.HasOne(x => x.CtEndEmployeeAgeType).WithMany().HasForeignKey(x => x.EndEmployeeAgeType).IsRequired(false);
        builder.HasOne(x => x.CtPayslipType).WithMany().HasForeignKey(x => x.PayslipType);
        builder.HasOne(x => x.CtMinimumMaximumType).WithMany().HasForeignKey(x => x.MinimumMaximumType);
    }
}