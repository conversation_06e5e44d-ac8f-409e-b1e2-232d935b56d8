namespace Vsp.PayrollConfiguration.Repository.Entities;

public class ModelYear : GeneratedIdEntity, IInheritanceEntity, IYearEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }

    public int? StandardShiftNumber { get; set; }
    public int? StandardEmployeeProfileNumber { get; set; }

    public int TestYear { get; set; }
    public int ZwSelfInsurerStartPayrollPeriodNumber { get; set; }
    public int Aof { get; set; }

    public DateOnly? DateAvailableEss { get; set; }

    public int SendEssMail { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;

    public CtYesNo CtTestYear { get; set; } = null!;
    public VwPayrollPeriod? ZwSelfInsurerStartPayrollPeriod { get; set; }
    public CtAof CtAof { get; set; } = null!;
    public VwStandardShift? VwStandardShift { get; set; }
    public EmployeeProfile? StandardEmployeeProfile { get; set; }
}