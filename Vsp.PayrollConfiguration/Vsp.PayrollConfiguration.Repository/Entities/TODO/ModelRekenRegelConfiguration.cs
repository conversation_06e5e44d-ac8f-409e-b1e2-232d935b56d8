namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelRekenRegelConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelRekenRegel>
{
    public override void Configure(EntityTypeBuilder<ModelRekenRegel> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelRekenRegel", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.JaarID, x.RekenRegelID });
    }
}
