namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class OnregelmatigeDienstConfiguration : GeneratedIdEntityTypeConfigurationBase<OnregelmatigeDienst>
{
    public override void Configure(EntityTypeBuilder<OnregelmatigeDienst> builder)
    {
        base.Configure(builder);

        builder.ToTable("OnregelmatigeDienst", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.<PERSON>, x.OnregelmatigeDienstID, x.VerloningsPeriodeID });
    }
}
