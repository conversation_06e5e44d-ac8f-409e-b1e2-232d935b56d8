namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelFondsGrafischConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelFondsGrafisch>
{
    public override void Configure(EntityTypeBuilder<ModelFondsGrafisch> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelFondsGrafisch", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.JaarID, x.FondsGrafischID, x.VerloningsPeriodeID });
    }
}
