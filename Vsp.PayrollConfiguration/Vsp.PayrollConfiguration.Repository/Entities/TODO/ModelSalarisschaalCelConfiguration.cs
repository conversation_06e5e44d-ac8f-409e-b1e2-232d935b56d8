namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelSalarisschaalCelConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelSalarisschaalCel>
{
    public override void Configure(EntityTypeBuilder<ModelSalarisschaalCel> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelSalarisschaalCel", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.SalarisschaalID, x.SalarisschaalCelID });
    }
}
