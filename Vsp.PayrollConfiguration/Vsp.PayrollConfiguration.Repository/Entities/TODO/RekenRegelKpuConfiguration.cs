namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class RekenRegelKpuConfiguration : GeneratedIdEntityTypeConfigurationBase<RekenRegelKpu>
{
    public override void Configure(EntityTypeBuilder<RekenRegelKpu> builder)
    {
        base.Configure(builder);

        builder.ToTable("RekenRegelKpu", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.Jaar<PERSON>, x.<PERSON> });
    }
}
