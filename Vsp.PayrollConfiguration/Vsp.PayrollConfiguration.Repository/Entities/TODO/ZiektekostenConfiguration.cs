namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ZiektekostenConfiguration : GeneratedIdEntityTypeConfigurationBase<Ziektekosten>
{
    public override void Configure(EntityTypeBuilder<Ziektekosten> builder)
    {
        base.Configure(builder);

        builder.ToTable("Ziektekosten", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.Jaar<PERSON>, x.ZiekteKostenID, x.VerloningsPeriodeID });
    }
}
