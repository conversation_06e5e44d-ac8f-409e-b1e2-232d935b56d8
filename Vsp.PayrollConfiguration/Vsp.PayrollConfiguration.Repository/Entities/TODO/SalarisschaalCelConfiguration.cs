namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class SalarisschaalCelConfiguration : GeneratedIdEntityTypeConfigurationBase<SalarisschaalCel>
{
    public override void Configure(EntityTypeBuilder<SalarisschaalCel> builder)
    {
        base.Configure(builder);

        builder.ToTable("SalarisschaalCel", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.SalarisschaalID, x.Sal<PERSON>schaalCelID });
    }
}
