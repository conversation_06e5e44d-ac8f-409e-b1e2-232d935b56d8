namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelSocialeUitkeringConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelSocialeUitkering>
{
    public override void Configure(EntityTypeBuilder<ModelSocialeUitkering> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelSocialeUitkering", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.JaarID, x.SocialeUitkeringID, x.VerloningsPeriodeID });
    }
}
