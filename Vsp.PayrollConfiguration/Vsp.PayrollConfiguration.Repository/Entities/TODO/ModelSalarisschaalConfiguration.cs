namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelSalarisschaalConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelSalarisschaal>
{
    public override void Configure(EntityTypeBuilder<ModelSalarisschaal> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelSalarisschaal", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.Sal<PERSON>schaalID });
    }
}
