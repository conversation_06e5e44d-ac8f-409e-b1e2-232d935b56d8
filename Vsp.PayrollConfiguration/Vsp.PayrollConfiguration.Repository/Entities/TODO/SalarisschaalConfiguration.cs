namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class SalarisschaalConfiguration : GeneratedIdEntityTypeConfigurationBase<Salarisschaal>
{
    public override void Configure(EntityTypeBuilder<Salarisschaal> builder)
    {
        base.Configure(builder);

        builder.ToTable("Salarisschaal", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.SalarisschaalID });
    }
}
