namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelSalarisschaalWaardeConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelSalarisschaalWaarde>
{
    public override void Configure(EntityTypeBuilder<ModelSalarisschaalWaarde> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelSalarisschaalWaarde", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.SalarisschaalID, x.<PERSON>schaalCelID, x.SalarisschaalWaardeID });
    }
}
