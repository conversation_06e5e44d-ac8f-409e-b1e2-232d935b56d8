namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class ModelSpaarregelingConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelSpaarregeling>
{
    public override void Configure(EntityTypeBuilder<ModelSpaarregeling> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelSpaarregeling", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.WerkgeverID, x.JaarID, x.SpaarregelingID, x.VerloningsPeriodeID });
    }
}
