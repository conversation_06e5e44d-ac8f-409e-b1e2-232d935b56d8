namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class FondsFranchiseVanafConfiguration : GeneratedIdEntityTypeConfigurationBase<FondsFranchiseVanaf>
{
    public override void Configure(EntityTypeBuilder<FondsFranchiseVanaf> builder)
    {
        base.Configure(builder);

        builder.ToTable("FondsFranchiseVanaf", "Ulsa");
        builder.<PERSON><PERSON>ey(x => new { x.WerkgeverID, x.<PERSON>, x.FondsID, x.FondsFranchiseVanafID, x.VerloningsPeriodeID });
    }
}
