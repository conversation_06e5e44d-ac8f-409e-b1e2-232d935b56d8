namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class InheritanceLevelConfiguration : IdEntityTypeConfigurationBase<InheritanceLevel>
{
    public override void Configure(EntityTypeBuilder<InheritanceLevel> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelWerkgever", "Ulsa");
        builder.<PERSON><PERSON>(x => x.InheritanceLevelId);

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ParentInheritanceLevelId).HasColumnName("AfhankelijkVanWerkgeverID");
        builder.Property(x => x.ProviderId).HasColumnName("AdministratieKantoorID");
        builder.Property(x => x.Description).HasColumnName("Omschrijving").HasColumnType("varchar(50)");
        builder.Property(x => x.Comment).HasColumnName("Opmerking").HasColumnType("varchar(4000)");

        builder.HasOne(x => x.InheritanceLevelInfo).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.ParentInheritanceLevel).WithMany().HasForeignKey(x => x.ParentInheritanceLevelId).IsRequired(false);
        builder.HasOne(x => x.Provider).WithMany().HasForeignKey(x => x.ProviderId).IsRequired(false);
    }
}
