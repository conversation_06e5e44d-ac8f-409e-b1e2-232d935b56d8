namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

/// <summary>
/// TODO: This should be used instead of <see cref="PayrollPeriod"/> everywhere!
/// </summary>
public class PayrollPeriodPayrollTaxReturn : GeneratedIdEntity
{
    [GeneratedIdKey<int>(0)]
    public int PayrollPeriodType { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int PayrollPeriodId { get; set; }

    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
}