namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class EmployeeWageResultComponent : IInheritanceEntity
{
    public int InheritanceLevelId { get; set; }
    public int YearId { get; set; }
    public int SequenceNumber { get; set; }
    public int PayrollEmployeeNumber { get; set; }
    public int PayrollPeriod { get; set; }
    public int PayslipType { get; set; }
    public int DistributionUnit { get; set; }
    public int ComponentId { get; set; }

    public decimal Amount { get; set; }
    public decimal RateAmount { get; set; }

    public InheritanceLevel InheritanceLevel { get; } = null!;
    public ModelComponent? ModelComponent { get; set; }
}