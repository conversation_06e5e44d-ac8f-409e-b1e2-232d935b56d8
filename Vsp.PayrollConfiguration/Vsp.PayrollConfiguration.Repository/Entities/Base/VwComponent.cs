namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class VwComponent : GeneratedIdEntity, IMutableCodeTable
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int ComponentId { get; set; }

    /// <summary>
    /// Description
    /// <para>Cannot be renamed due to <see cref="IMutableCodeTable"/></para>
    /// </summary>
    public string Omschrijving { get; set; } = null!;
}
