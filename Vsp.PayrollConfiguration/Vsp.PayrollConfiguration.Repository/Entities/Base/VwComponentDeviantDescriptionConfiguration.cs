namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class VwComponentDeviantDescriptionConfiguration : GeneratedIdEntityTypeConfigurationBase<VwComponentDeviantDescription>
{
    public override void Configure(EntityTypeBuilder<VwComponentDeviantDescription> builder)
    {
        base.Configure(builder);

        builder.ToView("ComponentNaamAfwijkend", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.ComponentId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");

        builder.Property(x => x.DeviatingDescription).HasColumnName("Naam").HasMaxLength(50);

        builder.Property(x => x.DeviatingDescriptionDefinedAtLevel).HasColumnName("NaamAttribuut");
        builder.Property(x => x.ComponentIdDefinedAtLevel).HasColumnName("ComponentIDAttribuut");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
    }
}