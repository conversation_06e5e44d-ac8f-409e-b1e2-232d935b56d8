namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class EmployeeWageResultComponentConfiguration : IEntityTypeConfiguration<EmployeeWageResultComponent>
{
    public void Configure(EntityTypeBuilder<EmployeeWageResultComponent> builder)
    {
        builder.ToTable("WerknemerLoonresultaatComponent", "Ulsa");

        builder.HasKey(x => new
        {
            x.InheritanceLevelId,
            x.YearId,
            x.SequenceNumber,
            x.PayrollEmployeeNumber,
            x.PayrollPeriod,
            x.PayslipType,
            x.DistributionUnit,
            x.ComponentId
        });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.SequenceNumber).HasColumnName("VolgnummerRun");
        builder.Property(x => x.PayrollEmployeeNumber).HasColumnName("WerknemerNummer");
        builder.Property(x => x.PayrollPeriod).HasColumnName("Periode");
        builder.Property(x => x.PayslipType).HasColumnName("Strook");
        builder.Property(x => x.DistributionUnit).HasColumnName("VerdelingsEenheid");
        builder.Property(x => x.ComponentId).HasColumnName("Component");
        builder.Property(x => x.Amount).HasColumnName("Inhoud");
        builder.Property(x => x.RateAmount).HasColumnName("InhoudTarief");

        builder.Property(x => x.Amount).HasColumnType("money");
        builder.Property(x => x.RateAmount).HasColumnType("money");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.ModelComponent).WithMany().IsRequired(false).HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ComponentId });
    }
}