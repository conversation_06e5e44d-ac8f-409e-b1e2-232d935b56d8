namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

public class VwComponentDeviantConfiguration : GeneratedIdEntityTypeConfigurationBase<VwComponentDeviant>
{
    public override void Configure(EntityTypeBuilder<VwComponentDeviant> builder)
    {
        base.Configure(builder);

        builder.ToView("ComponentAfwijkend", "Ulsa");
        builder.HasKey(x => new { x.InheritanceLevelId, x.ComponentId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");

        builder.Property(x => x.RouteType).HasColumnName("SoortRoute");

        builder.Property(x => x.RouteTypeDefinedAtLevel).HasColumnName("SoortRouteAttribuut");
        builder.Property(x => x.ComponentIdDefinedAtLevel).HasColumnName("ComponentIDAttribuut");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.CtRouteType).WithMany().HasForeignKey(x => x.RouteType);
    }
}