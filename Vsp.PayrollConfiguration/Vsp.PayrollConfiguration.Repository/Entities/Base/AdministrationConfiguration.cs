namespace Vsp.PayrollConfiguration.Repository.Entities.Base;

internal class AdministrationConfiguration : IdEntityTypeConfigurationBase<Administration>
{
    public override void Configure(EntityTypeBuilder<Administration> builder)
    {
        base.Configure(builder);

        builder.ToTable("Werkgever", "Ulsa");
        builder.HasKey(x => x.InheritanceLevelId);

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.EmployerId).HasColumnName("LoketWerkgeverID");
        builder.Property(x => x.Name).HasColumnName("Naam");
        builder.Property(x => x.ClientNumber).HasColumnName("ClientNummer");
        builder.Property(x => x.AdministrationNumber).HasColumnName("Administratienummer").HasColumnType("varchar(15)");
        builder.Property(x => x.GroupCode).HasColumnName("Groepcode").ValueGeneratedOnAdd();
        builder.Property(x => x.WageTaxNumber).HasColumnName("LoonheffingenNummer").IsRequired(false);
        builder.Property(x => x.PayrollId).HasColumnName("VerloningID");

        builder.HasOne(x => x.InheritanceLevel).WithOne(x => x.Administration).HasForeignKey<Administration>(x => x.InheritanceLevelId).IsRequired(false);
        builder.HasOne(x => x.Employer).WithMany().HasForeignKey(x => x.EmployerId);
    }
}
