namespace Vsp.PayrollConfiguration.Repository.Entities;

public class Fund : GeneratedIdEntity, IInheritanceEntity, IPayrollPeriodEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int FundId { get; set; }
    [GeneratedIdKey<short>(3)]
    public int PayrollPeriodId { get; set; } 
    // TODO: Complete model properties

    // DefinedAtLevel properties
    public int FundIdDefinedAtLevel { get; set; }
    // TODO: Complete DefinedAtLevel properties
    
    // Misc navigation properties
    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public Year Year { get; set; } = null!;
    public PayrollPeriod PayrollPeriod { get; set; } = null!;
    // TODO: Complete navigation properties
    
    // Code table navigation properties
    // TODO: Complete navigation properties
}