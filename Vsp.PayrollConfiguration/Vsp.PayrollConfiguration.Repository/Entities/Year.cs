namespace Vsp.PayrollConfiguration.Repository.Entities;

public class Year : GeneratedIdEntity, IInheritanceEntity, IYearEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }

    public int PayrollPeriodType { get; set; }
    public int StandardShiftNumber { get; set; }
    public int StandardEmployeeProfileNumber { get; set; }
    public int TestYear { get; set; }
    public int YearTransitionIsRequested { get; set; }
    public int YearTransitionIsPerformed { get; set; }
    public DateOnly? YearTransitionPerformedDate { get; set; }
    public DateOnly? DateAvailableEss { get; set; }
    public int SendEssMail { get; set; }
    public DateOnly? DateEssMail { get; set; }
    public int ZwSelfInsurerStartPayrollPeriodNumber { get; set; }
    public int Aof { get; set; }
    public int PayrollPeriodTypeDefinedAtLevel { get; set; }
    public int StandardShiftDefinedAtLevel { get; set; }
    public int StandardEmployeeProfileDefinedAtLevel { get; set; }

    public InheritanceLevel InheritanceLevel { get; set; } = null!;
    public CtPayrollPeriodType CtPayrollPeriodType { get; set; } = null!;
    public CtYesNo CtTestYear { get; set; } = null!;
    public CtYesNo CtYearTransitionIsRequested { get; set; } = null!;
    public CtYesNo CtYearTransitionIsPerformed { get; set; } = null!;
    public CtYesNo CtSendEssMail { get; set; } = null!;
    public CtAof CtAof { get; set; } = null!;
    public PayrollPeriod? ZwSelfInsurerStartPayrollPeriod { get; set; }
    public VwStandardShift? VwStandardShift { get; set; }
    public EmployeeProfile? StandardEmployeeProfile { get; set; }
    public ICollection<PayrollPeriod> PayrollPeriods { get; set; } = null!;
}