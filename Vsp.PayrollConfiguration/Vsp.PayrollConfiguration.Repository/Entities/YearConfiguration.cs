namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class YearConfiguration : GeneratedIdEntityTypeConfigurationBase<Year>
{
    public override void Configure(EntityTypeBuilder<Year> builder)
    {
        base.Configure(builder);

        builder.ToTable("Jaar", "Ulsa");
        builder.<PERSON><PERSON>ey(x => new { x.InheritanceLevelId, x.YearId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.PayrollPeriodType).HasColumnName("SoortVerloning");
        builder.Property(x => x.StandardShiftNumber).HasColumnName("StandaardPloegID");
        builder.Property(x => x.StandardEmployeeProfileNumber).HasColumnName("StandaardWerknemerProfielID");

        builder.Property(x => x.TestYear).HasColumnName("Test");
        builder.Property(x => x.YearTransitionIsRequested).HasColumnName("VerzoekTotAfsluiting");
        builder.Property(x => x.YearTransitionIsPerformed).HasColumnName("Afgesloten");
        builder.Property(x => x.YearTransitionPerformedDate).HasColumnName("DatumAfgesloten");
        builder.Property(x => x.DateAvailableEss).HasColumnName("DatumBeschikbaarEss");
        builder.Property(x => x.SendEssMail).HasColumnName("EssMailVersturen");
        builder.Property(x => x.DateEssMail).HasColumnName("DatumEssMail");
        builder.Property(x => x.ZwSelfInsurerStartPayrollPeriodNumber).HasColumnName("PeriodeEigenRisicoDragerZw");
        builder.Property(x => x.Aof).HasColumnName("SoortAof");
        builder.Property(x => x.PayrollPeriodTypeDefinedAtLevel).HasColumnName("SoortVerloningAttribuut");
        builder.Property(x => x.StandardShiftDefinedAtLevel).HasColumnName("StandaardPloegIDAttribuut");
        builder.Property(x => x.StandardEmployeeProfileDefinedAtLevel).HasColumnName("StandaardWerknemerProfielIDAttribuut");

        // TODO: Is it possible to remove this link to a MODEL entity? If possible MODEL should be joined to MODEL, and NON-MODEL to NON-MODEL!
        builder.HasOne(y => y.InheritanceLevel).WithMany().HasForeignKey(y => y.InheritanceLevelId);
        builder.HasOne(y => y.CtPayrollPeriodType).WithMany().HasForeignKey(y => y.PayrollPeriodType);

        builder.HasOne(y => y.CtTestYear).WithMany().HasForeignKey(y => y.TestYear);
        builder.HasOne(y => y.CtYearTransitionIsRequested).WithMany().HasForeignKey(y => y.YearTransitionIsRequested);
        builder.HasOne(y => y.CtYearTransitionIsPerformed).WithMany().HasForeignKey(y => y.YearTransitionIsPerformed);
        builder.HasOne(y => y.CtSendEssMail).WithMany().HasForeignKey(y => y.SendEssMail);
        builder.HasOne(y => y.CtAof).WithMany().HasForeignKey(y => y.Aof);
        builder.HasOne(y => y.ZwSelfInsurerStartPayrollPeriod).WithMany().IsRequired(false).HasForeignKey(y => new { y.InheritanceLevelId, y.YearId, y.ZwSelfInsurerStartPayrollPeriodNumber });
        builder.HasOne(y => y.VwStandardShift).WithMany().IsRequired(false).HasForeignKey(y => new { y.InheritanceLevelId, y.YearId, y.StandardShiftNumber });
        builder.HasOne(y => y.StandardEmployeeProfile).WithMany().IsRequired(false).HasForeignKey(y => new { y.InheritanceLevelId, y.YearId, y.StandardEmployeeProfileNumber });
    }
}

