namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelEmployeeProfileConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelEmployeeProfile>
{
    public override void Configure(EntityTypeBuilder<ModelEmployeeProfile> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelWerknemerProfiel", "Ulsa", x => x.<PERSON><PERSON>("trigger"));
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.EmployeeProfileId });
        
        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.EmployeeProfileId).HasColumnName("WerknemerProfielID");
    }
}