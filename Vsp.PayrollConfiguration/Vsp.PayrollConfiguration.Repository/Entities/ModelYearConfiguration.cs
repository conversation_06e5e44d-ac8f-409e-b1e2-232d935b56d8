namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelYearConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelYear>
{
    public override void Configure(EntityTypeBuilder<ModelYear> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelJaar", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.StandardShiftNumber).HasColumnName("StandaardPloegID").IsRequired(false);
        builder.Property(x => x.StandardEmployeeProfileNumber).HasColumnName("StandaardWerknemerProfielID").IsRequired(false);

        builder.Property(x => x.TestYear).HasColumnName("Test");
        builder.Property(x => x.ZwSelfInsurerStartPayrollPeriodNumber).HasColumnName("PeriodeEigenRisicoDragerZw");
        builder.Property(x => x.Aof).HasColumnName("SoortAof");
        builder.Property(x => x.DateAvailableEss).HasColumnName("DatumBeschikbaarEss");
        builder.Property(x => x.SendEssMail).HasColumnName("EssMailVersturen");

        builder.HasOne(my => my.InheritanceLevel).WithMany().HasForeignKey(my => my.InheritanceLevelId);

        builder.HasOne(my => my.CtTestYear).WithMany().HasForeignKey(my => my.TestYear);
        builder.HasOne(my => my.ZwSelfInsurerStartPayrollPeriod).WithMany().IsRequired(false).HasForeignKey(my => new { my.InheritanceLevelId, my.YearId, my.ZwSelfInsurerStartPayrollPeriodNumber });
        builder.HasOne(my => my.CtAof).WithMany().HasForeignKey(my => my.Aof);
        builder.HasOne(my => my.VwStandardShift).WithMany().IsRequired(false).HasForeignKey(my => new { my.InheritanceLevelId, my.YearId, my.StandardShiftNumber });
        builder.HasOne(my => my.StandardEmployeeProfile).WithMany().IsRequired(false).HasForeignKey(my => new { my.InheritanceLevelId, my.YearId, my.StandardEmployeeProfileNumber });
    }
}