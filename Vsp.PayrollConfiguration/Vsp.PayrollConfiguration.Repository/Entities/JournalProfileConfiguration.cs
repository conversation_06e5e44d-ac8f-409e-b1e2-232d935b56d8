namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class JournalProfileConfiguration : IdEntityTypeConfigurationBase<JournalProfile>
{
    public override void Configure(EntityTypeBuilder<JournalProfile> builder)
    {
        base.Configure(builder);

        builder.ToView("RekeningSchema", "Ulsa");
        builder.HasNoKey();

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.JournalProfileId).HasColumnName("RekeningSchemaID");
    }
}
