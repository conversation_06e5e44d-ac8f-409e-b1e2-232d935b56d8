namespace Vsp.PayrollConfiguration.Repository.Entities;

public class CalculationRule : GeneratedIdEntity
{
    [GeneratedIdKey<int>(0)]
    public int InheritanceLevelId { get; set; }
    [GeneratedIdKey<short>(1)]
    public int YearId { get; set; }
    [GeneratedIdKey<short>(2)]
    public int CalculationRuleId { get; set; }

    public int ResultComponentId { get; set; }

    public ModelComponent? ModelComponent { get; set; }
}