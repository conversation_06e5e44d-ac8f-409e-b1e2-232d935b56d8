namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelFundConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelFund>
{
    public override void Configure(EntityTypeBuilder<ModelFund> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelFonds", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>(x => new { x.InheritanceLevelId, x.YearId, x.FundId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.FundId).HasColumnName("FondsID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        // Misc Relationships
        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.VwPayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        // TODO: Complete relationship mappings
        
        // Code table relationships
        // TODO: Complete relationship mappings
    }
}