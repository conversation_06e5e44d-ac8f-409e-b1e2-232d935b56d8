namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class TeamUserConfiguration : IdEntityTypeConfigurationBase<TeamUser>
{
    public override void Configure(EntityTypeBuilder<TeamUser> builder)
    {
        base.Configure(builder);

        builder.ToTable("TeamGebruiker", "Loket");
        builder.HasKey(x => new { x.ProviderId, x.TeamId, x.UserId });

        builder.Property(x => x.ProviderId).HasColumnName("AdministratieKantoorID");
        builder.Property(x => x.TeamId).HasColumnName("TeamID");
        builder.Property(x => x.UserId).HasColumnName("GebruikerID");

        builder
            .HasOne(tu => tu.User)
            .WithMany(u => u.TeamUser)
            .HasForeignKey(tu => new { tu.ProviderId, tu.UserId })
            .HasPrincipalKey(u => new { u.ProviderId, u.UserId });

        builder
            .HasOne(tu => tu.Team)
            .WithMany(t => t.TeamUsers)
            .HasForeignKey(tu => new { tu.ProviderId, tu.TeamId })
            .HasPrincipalKey(t => new { t.ProviderId, t.TeamId });
    }
}
