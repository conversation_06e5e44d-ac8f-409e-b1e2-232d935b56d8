namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class ProviderConfiguration : IdEntityTypeConfigurationBase<Provider>
{
    public override void Configure(EntityTypeBuilder<Provider> builder)
    {
        base.Configure(builder);

        builder.ToTable("AdministratieKantoor", "dbo");
        builder.<PERSON><PERSON><PERSON>(x => x.ProviderId);

        builder.Property(x => x.ProviderId).HasColumnName("AdministratieKantoorID");
        builder.Property(x => x.Name).HasColumnName("Naam");
    }
}
