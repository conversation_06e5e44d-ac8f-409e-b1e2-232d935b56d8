namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class PayrollPeriodDataConfiguration : IdEntityTypeConfigurationBase<PayrollPeriodData>
{
    public override void Configure(EntityTypeBuilder<PayrollPeriodData> builder)
    {
        base.Configure(builder);

        builder.ToTable("Loontransactie", "Loket");
        builder.<PERSON><PERSON><PERSON>(x => new { x.EmployerId, x.PersonId, x.EmploymentId, x.WageTransactionId });

        builder.Property(x => x.EmployerId).HasColumnName("WerkgeverID");
        builder.Property(x => x.EmploymentId).HasColumnName("DienstverbandID");
        builder.Property(x => x.PersonId).HasColumnName("PersoonID");
        builder.Property(x => x.WageTransactionId).HasColumnName("LoontransactieID");
        builder.Property(x => x.PeriodId).HasColumnName("PeriodeID");

    }
}