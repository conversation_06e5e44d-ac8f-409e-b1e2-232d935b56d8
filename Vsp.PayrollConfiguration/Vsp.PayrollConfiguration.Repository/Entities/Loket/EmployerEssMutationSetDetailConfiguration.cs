namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class EmployerEssMutationSetDetailConfiguration : IdEntityTypeConfigurationBase<EmployerEssMutationSetDetail>
{
    public override void Configure(EntityTypeBuilder<EmployerEssMutationSetDetail> builder)
    {
        base.Configure(builder);

        builder.ToTable("WerkgeverExportSetDetail", "Loket");
        builder.HasKey(x => new { x.EmployerId, x.EmployerExportSetId, x.PayrollId, x.ComponentId });

        builder.Property(x => x.EmployerId).HasColumnName("WerkgeverID");
        builder.Property(x => x.EmployerExportSetId).HasColumnName("WerkgeverExportSetID");
        builder.Property(x => x.PayrollId).HasColumnName("VerloningID");
        builder.Property(x => x.ComponentId).HasColumnName("ComponentID");
    }
}
