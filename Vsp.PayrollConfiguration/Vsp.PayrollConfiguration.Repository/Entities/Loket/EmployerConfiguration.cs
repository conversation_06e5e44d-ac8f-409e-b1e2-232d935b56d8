namespace Vsp.PayrollConfiguration.Repository.Entities.Loket;

internal class EmployerConfiguration : IdEntityTypeConfigurationBase<Employer>
{
    public override void Configure(EntityTypeBuilder<Employer> builder)
    {
        base.Configure(builder);

        builder.ToTable("Werkgever", "Loket");
        builder.<PERSON><PERSON>ey(x => x.EmployerId);

        builder.Property(x => x.EmployerId).HasColumnName("WerkgeverID");
        builder.Property(x => x.Name).HasColumnName("Naam");
    }
}
