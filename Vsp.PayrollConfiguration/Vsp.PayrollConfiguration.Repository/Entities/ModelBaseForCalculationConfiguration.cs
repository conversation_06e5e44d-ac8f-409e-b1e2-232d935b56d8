namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class ModelBaseForCalculationConfiguration : GeneratedIdEntityTypeConfigurationBase<ModelBaseForCalculation>
{
    public override void Configure(EntityTypeBuilder<ModelBaseForCalculation> builder)
    {
        base.Configure(builder);

        builder.ToTable("ModelGrondslag", "Ulsa", x => x.<PERSON>("trigger"));
        builder.<PERSON><PERSON>ey(x => new { x.InheritanceLevelId, x.YearId, x.BaseForCalculationId, x.PayrollPeriodId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.YearId).HasColumnName("JaarID");
        builder.Property(x => x.BaseForCalculationId).HasColumnName("GrondslagID");
        builder.Property(x => x.PayrollPeriodId).HasColumnName("VerloningsPeriodeID");

        builder.Property(x => x.Description).HasColumnName("Omschrijving").HasMaxLength(50);
        builder.Property(x => x.BaseType).HasColumnName("SoortGrondslagLoonaangifte");
        builder.Property(x => x.StartEmployeeAgeType).HasColumnName("IngangsMoment");
        builder.Property(x => x.StartEmployeeAge).HasColumnName("IngangsMomentEenheden");
        builder.Property(x => x.EndEmployeeAgeType).HasColumnName("EindMoment");
        builder.Property(x => x.EndEmployeeAge).HasColumnName("EindMomentEenheden");
        builder.Property(x => x.ResultPayrollComponentId).HasColumnName("ResultaatComponentID");
        builder.Property(x => x.Percentage).HasColumnName("Percentage");
        builder.Property(x => x.CalculationPayrollPeriodNumber).HasColumnName("PeriodeBerekenen");
        builder.Property(x => x.ReferencePayrollPeriodNumber).HasColumnName("PeriodePeil");
        builder.Property(x => x.PayoutPayrollPeriodNumber).HasColumnName("PeriodeUitbetaling");
        builder.Property(x => x.AccrualEndPayrollPeriodNumber).HasColumnName("PeriodeEindeOpbouw");
        builder.Property(x => x.PayslipType).HasColumnName("Strook");
        builder.Property(x => x.IsPayoutAtStartOfEmployment).HasColumnName("VooruitbetalingBijInDienst");
        builder.Property(x => x.IsPayoutAtEndOfEmployment).HasColumnName("UitbetalingBijUitDienst");
        builder.Property(x => x.AdvancePayrollComponentId).HasColumnName("ReserveringsComponentID");
        builder.Property(x => x.AdvancePercentage).HasColumnName("ReserveringsPercentage");
        builder.Property(x => x.AdvancePayrollPeriodNumber).HasColumnName("PeriodeVoorschot");
        builder.Property(x => x.PeriodicReservationPayrollComponentId)
            .HasColumnName("PeriodiekeReserveringsComponentID");
        builder.Property(x => x.FinancialReservationPercentage).HasColumnName("ReserveringsPercentageFinancieel");
        builder.Property(x => x.FinancialMarkupPercentage).HasColumnName("OpslagPercentage");
        builder.Property(x => x.IsCumulativeCalculation).HasColumnName("CumulatieveGrondslag");
        builder.Property(x => x.IsPartTimeCalculation).HasColumnName("BerekeningViaDeeltijd");
        builder.Property(x => x.IsAutomaticCalculation).HasColumnName("AutomatischBerekenen");
        builder.Property(x => x.IsSupplementingDailyWage).HasColumnName("DagloonSuppleren");
        builder.Property(x => x.MinimumMaximumType).HasColumnName("SoortGrondslagMinimumMaximum");

        builder.HasOne(x => x.InheritanceLevel).WithMany().HasForeignKey(x => x.InheritanceLevelId);
        builder.HasOne(x => x.Year).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId });
        builder.HasOne(x => x.PayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.VwPayrollPeriod).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PayrollPeriodId });
        builder.HasOne(x => x.ResultPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ResultPayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.VwResultPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.ResultPayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.AdvancePayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, ReservationPayrollComponentId = x.AdvancePayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.VwAdvancePayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.AdvancePayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.PeriodicReservationPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PeriodicReservationPayrollComponentId }).IsRequired(false);
        builder.HasOne(x => x.VwPeriodicReservationPayrollComponent).WithMany().HasForeignKey(x => new { x.InheritanceLevelId, x.YearId, x.PeriodicReservationPayrollComponentId }).IsRequired(false);

        // Code table relationships
        builder.HasOne(x => x.CtBaseType).WithMany().HasForeignKey(x => x.BaseType).IsRequired(false);
        builder.HasOne(x => x.CtStartEmployeeAgeType).WithMany().HasForeignKey(x => x.StartEmployeeAgeType).IsRequired(false);
        builder.HasOne(x => x.CtEndEmployeeAgeType).WithMany().HasForeignKey(x => x.EndEmployeeAgeType).IsRequired(false);
        builder.HasOne(x => x.CtPayslipType).WithMany().HasForeignKey(x => x.PayslipType).IsRequired(false);
        builder.HasOne(x => x.CtIsPayoutAtStartOfEmployment).WithMany().HasForeignKey(x => x.IsPayoutAtStartOfEmployment).IsRequired(false);
        builder.HasOne(x => x.CtIsPayoutAtEndOfEmployment).WithMany().HasForeignKey(x => x.IsPayoutAtEndOfEmployment).IsRequired(false);
        builder.HasOne(x => x.CtIsCumulativeCalculation).WithMany().HasForeignKey(x => x.IsCumulativeCalculation).IsRequired(false);
        builder.HasOne(x => x.CtIsPartTimeCalculation).WithMany().HasForeignKey(x => x.IsPartTimeCalculation).IsRequired(false);
        builder.HasOne(x => x.CtIsAutomaticCalculation).WithMany().HasForeignKey(x => x.IsAutomaticCalculation).IsRequired(false);
        builder.HasOne(x => x.CtIsSupplementingDailyWage).WithMany().HasForeignKey(x => x.IsSupplementingDailyWage).IsRequired(false);
        builder.HasOne(x => x.CtMinimumMaximumType).WithMany().HasForeignKey(x => x.MinimumMaximumType).IsRequired(false);
    }
}