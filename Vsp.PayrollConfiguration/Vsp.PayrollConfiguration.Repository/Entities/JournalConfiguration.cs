namespace Vsp.PayrollConfiguration.Repository.Entities;

internal class JournalConfiguration : IdEntityTypeConfigurationBase<Journal>
{
    public override void Configure(EntityTypeBuilder<Journal> builder)
    {
        base.Configure(builder);

        builder.ToTable("<PERSON>urnaal", "Ulsa");
        builder.<PERSON><PERSON><PERSON>(x => new { x.InheritanceLevelId, x.JournalId });

        builder.Property(x => x.InheritanceLevelId).HasColumnName("WerkgeverID");
        builder.Property(x => x.JournalId).HasColumnName("JournaalID");
        builder.Property(x => x.JournalProfileId).HasColumnName("RekeningSchemaID");
    }
}
