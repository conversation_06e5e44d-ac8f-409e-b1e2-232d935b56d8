using System.ComponentModel.DataAnnotations;

namespace Vsp.PayrollConfiguration.Infrastructure.Models;

/// <summary>
/// Payroll period.
/// </summary>
public class PayrollPeriodPostModel : PayrollPeriodNumberModel
{
    /// <summary>
    /// The year of the payroll period.
    /// </summary>
    /// <example>2025</example>
    [Required]
    [Range(1900, 9999)]
    public int? Year { get; set; } = null!;
}