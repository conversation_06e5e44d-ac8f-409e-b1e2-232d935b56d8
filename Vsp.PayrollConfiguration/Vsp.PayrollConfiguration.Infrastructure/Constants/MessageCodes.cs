namespace Vsp.PayrollConfiguration.Infrastructure.Constants;

public static class MessageCodes
{
    public const string API_NotFound = nameof(API_NotFound);

    // POST
    public const string API_PayrollConfiguration_Insert_Year_DoesNotExist = nameof(API_PayrollConfiguration_Insert_Year_DoesNotExist);
    public const string API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist = nameof(API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist);
    public const string API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist = nameof(API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist);
    public const string API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel = nameof(API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel);
    public const string API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel = nameof(API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel);

    public const string API_PayrollConfiguration_Insert_Entity_AddedToFutureYear = nameof(API_PayrollConfiguration_Insert_Entity_AddedToFutureYear);
    public const string API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear = nameof(API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear);

    public const string API_PayrollConfiguration_Insert_Entity_AddedArrangementPayrollComponent = nameof(API_PayrollConfiguration_Insert_Entity_AddedArrangementPayrollComponent);

    // DELETE
    public const string API_PayrollConfiguration_Delete_EntityHasChildren = nameof(API_PayrollConfiguration_Delete_EntityHasChildren);
    public const string API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted = nameof(API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted);

    // UNIT PERCENTAGE
    public const string API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid = nameof(API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid);
    public const string API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid = nameof(API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid);

    // DIFFERENTIATED RETURN TO WORK FUND
    public const string API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaZwTotalContributionOutOfRange = nameof(API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaZwTotalContributionOutOfRange);
    public const string API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero = nameof(API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero);
    public const string API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf = nameof(API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf);

    // YEAR
    public const string API_PayrollConfiguration_Year_StandardShift_Invalid = nameof(API_PayrollConfiguration_Year_StandardShift_Invalid);
    public const string API_PayrollConfiguration_Year_StandardEmployeeProfile_Invalid = nameof(API_PayrollConfiguration_Year_StandardEmployeeProfile_Invalid);
    public const string API_PayrollConfiguration_Year_TestYear_PreviousYearNotTest = nameof(API_PayrollConfiguration_Year_TestYear_PreviousYearNotTest);
    public const string API_PayrollConfiguration_Year_TestYear_PayrollTaxReturnPerformed = nameof(API_PayrollConfiguration_Year_TestYear_PayrollTaxReturnPerformed);
    public const string API_PayrollConfiguration_Year_ZwSelfInsurerStartPayrollPeriod_Invalid = nameof(API_PayrollConfiguration_Year_ZwSelfInsurerStartPayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_Year_Aof_Invalid = nameof(API_PayrollConfiguration_Year_Aof_Invalid);
    public const string API_PayrollConfiguration_Year_Aof_MustNotHaveValueBefore2022 = nameof(API_PayrollConfiguration_Year_Aof_MustNotHaveValueBefore2022);
    public const string API_PayrollConfiguration_Year_Aof_MustHaveValueFrom2022 = nameof(API_PayrollConfiguration_Year_Aof_MustHaveValueFrom2022);
    public const string API_PayrollConfiguration_Year_DateAvailableEss_OldDateAvailableEssNotEmptyAndNotInFuture = nameof(API_PayrollConfiguration_Year_DateAvailableEss_OldDateAvailableEssNotEmptyAndNotInFuture);
    public const string API_PayrollConfiguration_Year_DateAvailableEss_YearClosureNotRequestedAndYearNotClosed = nameof(API_PayrollConfiguration_Year_DateAvailableEss_YearClosureNotRequestedAndYearNotClosed);
    public const string API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssNotEmptyAndNotInFuture = nameof(API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssNotEmptyAndNotInFuture);
    public const string API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssEmptyAndYearClosed = nameof(API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssEmptyAndYearClosed);
    public const string API_PayrollConfiguration_Year_SendEssMail_YearClosureNotRequestedAndYearNotClosed = nameof(API_PayrollConfiguration_Year_SendEssMail_YearClosureNotRequestedAndYearNotClosed);
    public const string API_PayrollConfiguration_Year_SendEssMail_OldDateAvailableEssNotEmptyAndNotInFuture = nameof(API_PayrollConfiguration_Year_SendEssMail_OldDateAvailableEssNotEmptyAndNotInFuture);

    // PAYROLL COMPONENT
    public const string API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_PaymentPeriod_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_PaymentPeriod_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_TaxLiable_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_TaxLiable_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_HoursIndication_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_HoursIndication_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_CostsEmployer_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_CostsEmployer_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_BaseForCalculationBter_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_BaseForCalculationBter_Invalid);

    public const string API_PayrollConfiguration_PayrollComponent_Post_Invalid = nameof(API_PayrollConfiguration_PayrollComponent_Post_Invalid);
    public const string API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_1 = nameof(API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_1);
    public const string API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_2 = nameof(API_PayrollConfiguration_PayrollComponent_DeductionOrPayment_2);
    public const string API_PayrollConfiguration_PayrollComponent_TaxLiable_1 = nameof(API_PayrollConfiguration_PayrollComponent_TaxLiable_1);
    public const string API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_1 = nameof(API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_1);
    public const string API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_2 = nameof(API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_2);
    public const string API_PayrollConfiguration_PayrollComponent_PaymentPeriod_1 = nameof(API_PayrollConfiguration_PayrollComponent_PaymentPeriod_1);
    public const string API_PayrollConfiguration_PayrollComponent_IsPayment_1 = nameof(API_PayrollConfiguration_PayrollComponent_IsPayment_1);
    public const string API_PayrollConfiguration_PayrollComponent_IsPayment_2 = nameof(API_PayrollConfiguration_PayrollComponent_IsPayment_2);
    public const string API_PayrollConfiguration_PayrollComponent_PaymentDescription_1 = nameof(API_PayrollConfiguration_PayrollComponent_PaymentDescription_1);
    public const string API_PayrollConfiguration_PayrollComponent_CostsEmployer_1 = nameof(API_PayrollConfiguration_PayrollComponent_CostsEmployer_1);
    public const string API_PayrollConfiguration_PayrollComponent_CostsEmployer_2 = nameof(API_PayrollConfiguration_PayrollComponent_CostsEmployer_2);
    public const string API_PayrollConfiguration_PayrollComponent_CostsEmployer_3 = nameof(API_PayrollConfiguration_PayrollComponent_CostsEmployer_3);
    public const string API_PayrollConfiguration_PayrollComponent_IsNetToGross_1 = nameof(API_PayrollConfiguration_PayrollComponent_IsNetToGross_1);
    public const string API_PayrollConfiguration_PayrollComponent_IsNetToGross_2 = nameof(API_PayrollConfiguration_PayrollComponent_IsNetToGross_2);
    public const string API_PayrollConfiguration_PayrollComponent_IsNetToGross_3 = nameof(API_PayrollConfiguration_PayrollComponent_IsNetToGross_3);
    public const string API_PayrollConfiguration_PayrollComponent_IsNetToGross_4 = nameof(API_PayrollConfiguration_PayrollComponent_IsNetToGross_4);
    public const string API_PayrollConfiguration_PayrollComponent_IsNetToGross_5 = nameof(API_PayrollConfiguration_PayrollComponent_IsNetToGross_5);
    public const string API_PayrollConfiguration_PayrollComponent_IsOvertime_1 = nameof(API_PayrollConfiguration_PayrollComponent_IsOvertime_1);
    public const string API_PayrollConfiguration_PayrollComponent_IsOvertime_2 = nameof(API_PayrollConfiguration_PayrollComponent_IsOvertime_2);
    public const string API_PayrollConfiguration_PayrollComponent_Column_1 = nameof(API_PayrollConfiguration_PayrollComponent_Column_1);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52001 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52001);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52002 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52002);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52003 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52003);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52004 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52004);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52005 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52005);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52006 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52006);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52007 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52007);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52008 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52008);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52009 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52009);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52010 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52010);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52011 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52011);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52012 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52012);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52013 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52013);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52015 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52015);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52016 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52016);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52017 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52017);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52018 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52018);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52019 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52019);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52020 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52020);
    public const string API_PayrollConfiguration_PayrollComponent_Delete_52021 = nameof(API_PayrollConfiguration_PayrollComponent_Delete_52021);

    // PAYROLL COMPONENT EXTRA
    public const string API_PayrollConfiguration_PayrollComponentExtra_DeviatingDescription_EmptyOrWhitespace = nameof(API_PayrollConfiguration_PayrollComponentExtra_DeviatingDescription_EmptyOrWhitespace);
    public const string API_PayrollConfiguration_PayrollComponentExtra_RouteType_NotApplicable = nameof(API_PayrollConfiguration_PayrollComponentExtra_RouteType_NotApplicable);
    public const string API_PayrollConfiguration_PayrollComponentExtra_RouteType_InvalidKey = nameof(API_PayrollConfiguration_PayrollComponentExtra_RouteType_InvalidKey);

    // BASE FOR CALCULATION
    public const string API_PayrollConfiguration_BaseForCalculation_BaseType_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_BaseType_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_StartEmployeeAgeType_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_StartEmployeeAgeType_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_EndEmployeeAgeType_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_EndEmployeeAgeType_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_PayslipType_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_PayslipType_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculation_MinimumMaximumType_Invalid = nameof(API_PayrollConfiguration_BaseForCalculation_MinimumMaximumType_Invalid);

    public const string API_PayrollConfiguration_BaseForCalculation_Post_Key_ReservedCLA = nameof(API_PayrollConfiguration_BaseForCalculation_Post_Key_ReservedCLA);
    public const string API_PayrollConfiguration_BaseForCalculation_BaseType_1 = nameof(API_PayrollConfiguration_BaseForCalculation_BaseType_1);
    public const string API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_1 = nameof(API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_1);
    public const string API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_2 = nameof(API_PayrollConfiguration_BaseForCalculation_StartEmployeeAge_2);
    public const string API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_1 = nameof(API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_1);
    public const string API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_2 = nameof(API_PayrollConfiguration_BaseForCalculation_EndEmployeeAge_2);
    public const string API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_2 = nameof(API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_2);
    public const string API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_1 = nameof(API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_1);
    public const string API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2 = nameof(API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2);
    public const string API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_1 = nameof(API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_1);
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_1 = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_1);
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_2 = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_2);
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_3 = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_3);
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_4 = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_4);
    public const string API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_1 = nameof(API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_2 = nameof(API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_2);
    public const string API_PayrollConfiguration_BaseForCalculation_IsPayoutAtStartOfEmployment_1 = nameof(API_PayrollConfiguration_BaseForCalculation_IsPayoutAtStartOfEmployment_1);
    public const string API_PayrollConfiguration_BaseForCalculation_IsPayoutAtEndOfEmployment_1 = nameof(API_PayrollConfiguration_BaseForCalculation_IsPayoutAtEndOfEmployment_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_1 = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_3 = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_3);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_4 = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_4);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePercentage_1 = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePercentage_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_1 = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollPeriod_1);
    public const string API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_1 = nameof(API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_1);
    public const string API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_3 = nameof(API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_3);
    public const string API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_4 = nameof(API_PayrollConfiguration_BaseForCalculation_PeriodicReservationPayrollComponent_4);
    public const string API_PayrollConfiguration_BaseForCalculation_FinancialReservationPercentage_1 = nameof(API_PayrollConfiguration_BaseForCalculation_FinancialReservationPercentage_1);
    public const string API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_1 = nameof(API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_1);
    public const string API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_2 = nameof(API_PayrollConfiguration_BaseForCalculation_FinancialMarkupPercentage_2);
    public const string API_PayrollConfiguration_BaseForCalculation_IsPartTimeCalculation_1 = nameof(API_PayrollConfiguration_BaseForCalculation_IsPartTimeCalculation_1);
    public const string API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_1 = nameof(API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_1);
    public const string API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_2 = nameof(API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_2);
    public const string API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_3 = nameof(API_PayrollConfiguration_BaseForCalculation_IsAutomaticCalculation_3);
    public const string API_PayrollConfiguration_BaseForCalculation_1 = nameof(API_PayrollConfiguration_BaseForCalculation_1);
    
    public const string API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_BasePayrollComponent = nameof(API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_BasePayrollComponent);
    public const string API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMinimum = nameof(API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMinimum);
    public const string API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMaximum = nameof(API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMaximum);
    public const string API_PayrollConfiguration_BaseForCalculation_Delete_InUse_EmploymentProfile = nameof(API_PayrollConfiguration_BaseForCalculation_Delete_InUse_EmploymentProfile);

    // BASE FOR CALCULATION WARNINGS
    public const string API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Warning_1 = nameof(API_PayrollConfiguration_BaseForCalculation_PayoutPayrollPeriod_Warning_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_1 = nameof(API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_1);
    public const string API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_2 = nameof(API_PayrollConfiguration_BaseForCalculation_AccrualEndPayrollPeriod_Warning_2);
    public const string API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_1 = nameof(API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_1);
    public const string API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_2 = nameof(API_PayrollConfiguration_BaseForCalculation_ReferencePayrollPeriod_Warning_2);
    public const string API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_AdvancePayrollComponent_Equal = nameof(API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_AdvancePayrollComponent_Equal);
    public const string API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_PeriodicReservationPayrollComponent_Equal = nameof(API_PayrollConfiguration_BaseForCalculation_ResultPayrollComponent_PeriodicReservationPayrollComponent_Equal);
    public const string API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_PeriodicReservationPayrollComponent_Equal = nameof(API_PayrollConfiguration_BaseForCalculation_AdvancePayrollComponent_PeriodicReservationPayrollComponent_Equal);
    
    // BASE FOR CALCULATION BASE PAYROLL COMPONENT 
    public const string API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid = nameof(API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid = nameof(API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid);
    public const string API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_1 = nameof(API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_1);
    public const string API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid = nameof(API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid);
}