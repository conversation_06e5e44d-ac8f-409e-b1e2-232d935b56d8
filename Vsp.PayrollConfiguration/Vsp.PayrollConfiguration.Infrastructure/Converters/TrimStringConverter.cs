namespace Vsp.PayrollConfiguration.Infrastructure.Converters;

public class TrimStringConverter : JsonConverter<string>
{
    public override string? Read<PERSON>son(JsonReader reader, Type objectType, string? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var value = reader.Value as string;
        return value?.Trim();
    }

    public override void Write<PERSON><PERSON>(JsonWriter writer, string? value, JsonSerializer serializer) => writer.WriteValue(value);
}