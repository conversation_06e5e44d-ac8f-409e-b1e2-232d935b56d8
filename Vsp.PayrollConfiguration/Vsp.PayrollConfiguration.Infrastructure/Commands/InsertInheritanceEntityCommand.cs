using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Commands;

public abstract class InsertInheritanceEntityCommand<TPostModel, TModel, TEntity, TModelEntity>(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<TModel, TEntity> query,
    IServiceProvider serviceProvider)
    : BaseCommand<TPostModel, TModel, TModelEntity, ILoketContext>(dependencies),
        IInsertInheritanceEntityCommand<TPostModel, TModel, TEntity, TModelEntity>
    where TPostModel : class, IPostModel
    where TModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly IGetInheritanceEntityQuery<TModel, TEntity> query = query;
    private readonly IServiceProvider serviceProvider = serviceProvider;

    /// <summary>
    /// Based on: <see cref="InsertCommand{TModelIn, TModelOut, TEntity, IContext}.Execute(TModelIn)"/>
    /// </summary>
    public async Task<IOperationResult<TModel>> ExecuteAsync(TPostModel postModel)
    {
        try
        {
            // Sanity checks TODO: Convert this to a reflection-based unit test with EVEN MORE checks! :)
            if (!typeof(IYearEntity).IsAssignableFrom(typeof(TEntity)) && !typeof(IPayrollPeriodEntity).IsAssignableFrom(typeof(TEntity)))
            {
                throw new InvalidOperationException(
                    $"Entity is neither {nameof(IYearEntity)} or {nameof(IPayrollPeriodEntity)}: {typeof(TEntity).Name}");
            }
            if (!typeof(IYearEntity).IsAssignableFrom(typeof(TModelEntity)) && !typeof(IPayrollPeriodEntity).IsAssignableFrom(typeof(TModelEntity)))
            {
                throw new InvalidOperationException(
                    $"Model entity is neither {nameof(IYearEntity)} or {nameof(IPayrollPeriodEntity)}: {typeof(TModelEntity).Name}");
            }
            if (!typeof(IYearPostModel).IsAssignableFrom(typeof(TPostModel)) && !typeof(IPayrollPeriodPostModel).IsAssignableFrom(typeof(TPostModel)))
            {
                throw new InvalidOperationException(
                    $"POST model is neither {nameof(IYearPostModel)} or {nameof(IPayrollPeriodPostModel)}: {typeof(TEntity).Name}");
            }

            // Execute validation logic like regular commands
            await BeforeValidationAsync(postModel);
            var validationMessages = (await ExecuteValidationRules(postModel, null)).ToList();
            var validationError = validationMessages.Any(x => x.MessageCode == ValidationConstants.INFO_ValidateOnly
                || (x.MessageType != MessageTypeEnum.Info && x.MessageType != MessageTypeEnum.Warning));

            var modelEntity = await MapPostModelToModelEntityAsync(postModel);
            if (!validationError)
            {
                await AfterValidationAsync(postModel);

                // First add arrangement payroll components if needed, and stop if that fails
                var addArrangementPayrollComponentsMessages = await AddArrangementPayrollComponentsAsync(postModel, modelEntity);
                validationMessages.AddRange(addArrangementPayrollComponentsMessages);
                if (addArrangementPayrollComponentsMessages.Any(x => x.MessageCode == ValidationConstants.INFO_ValidateOnly
                    || (x.MessageType != MessageTypeEnum.Info && x.MessageType != MessageTypeEnum.Warning)))
                {
                    return new OperationResult<TModel>(validationMessages);
                }
                // Proceed with main entity insertion after arrangement payroll components are successfully added
                else
                {
                    await ExecuteInsert(postModel, modelEntity);
                }
            }

            // NOTE: In the context of adding arrangement payroll components, we never want to add future years:
            // the components will be added when the future years of the main entity are added!
            // NOTE: In case of validation errors on the main year entity - do NOT attempt to add future years!
            // But even in case of validation errors on a future year entity - we DO want to attempt to add more years after that!
            if (!this.IsAddArrangementPayrollComponentsChildCommand && (!validationError || this.IsAddFutureYearsChildCommand))
            {
                var addFutureYearsMessages = await AddFutureYearsAsync(postModel, modelEntity);
                validationMessages.AddRange(addFutureYearsMessages);
            }

            if (validationError)
            {
                return new OperationResult<TModel>(validationMessages);
            }
            else
            {
                var model = await GetResultAfterInsert(modelEntity.Id);
                return new OperationResult<TModel>(model, validationMessages);
            }
        }
        // Handle exceptions like regular commands
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    private async Task<TModelEntity> MapPostModelToModelEntityAsync(TPostModel postModel)
    {
        this.inheritanceLevel = await this.dbContext.Set<InheritanceLevel>().AsNoTracking()
            .Where(x => x.Id == postModel.InheritanceLevelId)
            .SingleAsync();

        var modelEntity = this.mapper.Map<TModelEntity>(postModel, opt =>
            opt.Items[nameof(IInheritanceEntity.InheritanceLevelId)] = this.inheritanceLevel.InheritanceLevelId);
        return modelEntity;
    }

    private async Task<TModelEntity> ExecuteInsert(TPostModel postModel, TModelEntity modelEntity)
    {
        this.dbContext.Add(modelEntity);
        await BeforeCommitAsync(postModel, modelEntity);

        // Create audit trail like regular commands
        var auditTrailRecords = CreateAuditTrail(modelEntity);
        await this.dbContext.SaveChangesAsync();
        await ProcessAuditTrailAsync(auditTrailRecords);

        // After execute like regular commands
        try
        {
            await AfterExecuteAsync(postModel, modelEntity);
        }
        catch (Exception ex) // consume exceptions in after execute
        {
            this.logger.LogWarning(ex, "Error in after execute for: {Command}", GetType().Name);
        }

        return modelEntity;
    }

    /// <summary>
    /// Based on: <see cref="InsertCommand{TModelIn, TModelOut, TEntity, IContext}.CreateAuditTrail(TEntity)"/>
    /// </summary>
    protected override IEnumerable<AuditTrailRecord> CreateAuditTrail(TModelEntity modelEntity)
    {
        if (modelEntity is ITrackableEntity trackableEntity)
        {
            trackableEntity.CreatedOn = DateTime.Now;
            trackableEntity.CreatedBy = this.currentContext.UserId;
        }

        return base.CreateAuditTrail(modelEntity);
    }

    /// <summary>
    /// Based on: <see cref="InsertCommand{TModelIn, TModelOut, TEntity, IContext}.GetResultAfterInsert(Guid)"/>
    /// </summary>
    private async Task<TModel> GetResultAfterInsert(Guid id)
    {
        var result = await this.query.ExecuteAsync(id);
        return result.Success
            ? result.ResultObject
            : throw new InvalidOperationException($"{string.Concat(result.Messages.Select(x => $"{x.MessageType} {x.MessageCode} {x.Description}"), '\n')}");
    }

    #region AddFutureYears

    /// <summary>
    /// Add future years? When adding to an older year, this automatically adds the entity to following years as well (if it doesn't exist there yet).
    /// </summary>
    protected abstract bool AddFutureYears { get; }

    /// <summary>
    /// Whether this is a child command in the context of: <see cref="AddFutureYearsAsync"/>
    /// </summary>
    public bool IsAddFutureYearsChildCommand { get; set; } = false;

    /// <summary>
    /// For caching purposes in the context of: <see cref="AddFutureYearsAsync"/>
    /// </summary>
    private InheritanceLevel inheritanceLevel = null!;

    private async Task<IList<OperationMessage>> AddFutureYearsAsync(TPostModel postModel, TModelEntity modelEntity)
    {
        if (this.AddFutureYears)
        {
            // NOTE: The order matters here, since IPayrollPeriodPostModel inherits from IYearPostModel!
            if (postModel is IPayrollPeriodPostModel payrollPeriodPostModel && modelEntity is IPayrollPeriodEntity payrollPeriodEntity)
            {
                return await AddNextYearAsync(payrollPeriodPostModel, payrollPeriodEntity);
            }
            else if (postModel is IYearPostModel yearPostModel && modelEntity is IYearEntity yearEntity)
            {
                return await AddNextYearAsync(yearPostModel, yearEntity);
            }
            else
            {
                throw new NotImplementedException($"Unhandled POST model type for future year addition: {postModel.GetType().Name}");
            }
        }

        return [];
    }

    private async Task<IList<OperationMessage>> AddNextYearAsync(IYearPostModel postModel, IYearEntity modelEntity)
    {
        var nextYear = GetNextYear(modelEntity);
        var nextYearExists = await NextYearExistsAsync(nextYear);
        if (!nextYearExists) return [];

        var modelEntityCopy = this.mapper.Map<TModelEntity>(modelEntity);
        if (ReferenceEquals(modelEntity, modelEntityCopy))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {typeof(TModelEntity).Name}");
        }
        ((IYearEntity)modelEntityCopy).YearId = nextYear;
        GeneratedIdHelper.GenerateId(modelEntityCopy); // Force ID re-generation because of new YearId

        var nextYearEntityExists = await this.dbContext.Set<TEntity>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<TEntity>(modelEntityCopy.Id))
            .AnyAsync();
        if (nextYearEntityExists) return [];

        var postModelCopy = this.mapper.Map<TPostModel>(postModel);
        if (ReferenceEquals(postModel, postModelCopy))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {typeof(TPostModel).Name}");
        }
        postModelCopy.InheritanceLevelId = postModel.InheritanceLevelId;
        ((IYearPostModel)postModelCopy).Year = nextYear;

        return await AddNextYearEntityAsync(nextYear, postModelCopy);
    }

    private async Task<IList<OperationMessage>> AddNextYearAsync(IPayrollPeriodPostModel postModel, IPayrollPeriodEntity modelEntity)
    {
        var nextYear = GetNextYear(modelEntity);
        var nextYearExists = await NextYearExistsAsync(nextYear);
        if (!nextYearExists) return [];

        var modelEntityCopy = this.mapper.Map<TModelEntity>(modelEntity);
        if (ReferenceEquals(modelEntity, modelEntityCopy))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {typeof(TModelEntity).Name}");
        }
        ((IPayrollPeriodEntity)modelEntityCopy).YearId = nextYear;
        ((IPayrollPeriodEntity)modelEntityCopy).PayrollPeriodId = 0;
        GeneratedIdHelper.GenerateId(modelEntityCopy); // Force ID re-generation because of new YearId and new PayrollPeriodId

        var nextYearEntityExists = await this.dbContext.Set<TEntity>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<TEntity>(modelEntityCopy.Id, new Dictionary<string, GeneratedIdKeyComparison>()
            {
                { nameof(IPayrollPeriodEntity.PayrollPeriodId), GeneratedIdKeyComparison.GreaterThan }, // SELECT PayrollPeriodId > 0
            }))
            .AnyAsync();
        if (nextYearEntityExists) return [];

        var postModelCopy = this.mapper.Map<TPostModel>(postModel);
        if (ReferenceEquals(postModel, postModelCopy))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {typeof(TPostModel).Name}");
        }
        if (ReferenceEquals(postModel.StartPayrollPeriod, ((IPayrollPeriodPostModel)postModelCopy).StartPayrollPeriod))
        {
            throw new InvalidOperationException($"Missing type map for cloning: {nameof(PayrollPeriodNumberModel)}");
        }
        postModelCopy.InheritanceLevelId = postModel.InheritanceLevelId;
        ((IPayrollPeriodPostModel)postModelCopy).Year = nextYear;
        ((IPayrollPeriodPostModel)postModelCopy).StartPayrollPeriod.PeriodNumber = 1; // Always add future entities with period 1 (Classic behavior)

        return await AddNextYearEntityAsync(nextYear, postModelCopy);
    }

    private static int GetNextYear(IYearEntity modelEntity) => modelEntity.YearId + 1;

    private async Task<bool> NextYearExistsAsync(int nextYear) =>
        await this.dbContext.Years.AsNoTracking()
            .Where(x => x.InheritanceLevelId == this.inheritanceLevel.InheritanceLevelId && x.YearId == nextYear)
            .AnyAsync();

    private async Task<IList<OperationMessage>> AddNextYearEntityAsync(int nextYear, TPostModel postModelCopy)
    {
        using var scope = this.serviceProvider.CreateScope();
        var childCommand = scope.ServiceProvider.GetRequiredService<IInsertInheritanceEntityCommand<TPostModel, TModel, TEntity, TModelEntity>>();
        childCommand.IsAddFutureYearsChildCommand = true;

        var childResult = await childCommand.ExecuteAsync(postModelCopy);
        var childMessages = childResult.Messages
            .Where(x => x.MessageType == MessageTypeEnum.Info || x.MessageType == MessageTypeEnum.Warning)
            // We don't emit messages about validate only for future years, as this would lead to duplication
            .Where(x => x.MessageCode != ValidationConstants.INFO_ValidateOnly)
            // We don't emit messages about adding arrangement payroll components for future years, as this would lead to duplication
            .Where(x => x.MessageCode != MessageCodes.API_PayrollConfiguration_Insert_Entity_AddedArrangementPayrollComponent)
            .ToList();
        if (!childResult.Success)
        {
            var message = childMessages.Where(x => x.MessageCode == MessageCodes.API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear).FirstOrDefault();
            if (message == null)
            {
                message = new OperationMessage(MessageTypeEnum.Warning, MessageCodes.API_PayrollConfiguration_Insert_Entity_NotAddedToFutureYear,
                    $"Failed to automatically add entity to future year(s) as well. See properties for details.")
                {
                    Properties = $"[{nextYear}]" // Custom property should be valid JSON array of years (int)
                };
                childMessages.Insert(0, message);
            }
            else
            {
                message.Properties = $"[{nextYear}, {message.Properties!.Trim('[')}"; // Custom property should be valid JSON array of years (int)
            }
            return childMessages;
        }
        else
        {
            var message = childMessages.Where(x => x.MessageCode == MessageCodes.API_PayrollConfiguration_Insert_Entity_AddedToFutureYear).FirstOrDefault();
            if (message == null)
            {
                message = new OperationMessage(MessageTypeEnum.Info, MessageCodes.API_PayrollConfiguration_Insert_Entity_AddedToFutureYear,
                    $"Automatically added entity to future year(s) as well. See properties for details.")
                {
                    Properties = $"[{nextYear}]" // Custom property should be valid JSON array of years (int)
                };
                childMessages.Insert(0, message);
            }
            else
            {
                message.Properties = $"[{nextYear}, {message.Properties!.Trim('[')}"; // Custom property should be valid JSON array of years (int)
            }
            return childMessages;
        }
    }

    #endregion

    #region AddArrangementPayrollComponents

    /// <summary>
    /// Whether this is a child command in the context of: <see cref="AddArrangementPayrollComponentsAsync"/>
    /// </summary>
    public bool IsAddArrangementPayrollComponentsChildCommand { get; set; } = false;

    /// <summary>
    /// Add arrangement payroll components? This automatically adds payroll components that are required for the entity to function properly.
    /// </summary>
    protected virtual Task<IList<OperationMessage>> AddArrangementPayrollComponentsAsync(TPostModel postModel, TModelEntity modelEntity) =>
        Task.FromResult<IList<OperationMessage>>([]);

    #endregion
}