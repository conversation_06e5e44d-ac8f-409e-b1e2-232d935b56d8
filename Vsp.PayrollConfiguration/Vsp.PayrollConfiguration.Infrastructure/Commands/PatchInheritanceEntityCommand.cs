using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Infrastructure.Commands;

public abstract class PatchInheritanceEntityCommand<TPatchModel, TModel, TEntity, TModelEntity>(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<TModel, TEntity> query)
    : BaseCommand<TPatchModel, TModel, TModelEntity, ILoketContext>(dependencies),
        IPatchInheritanceEntityCommand<TPatchModel, TModel, TEntity, TModelEntity>
    where TPatchModel : class, IPatchModel
    where TModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly IGetInheritanceEntityQuery<TModel, TEntity> query = query;

    /// <summary>
    /// Update only? This blocks create and delete of model records.
    /// </summary>
    protected abstract bool UpdateOnly { get; }

    /// <summary>
    /// The properties that are set automatically based on the given patch model.
    /// <para>To set additional (custom) properties, use: <see cref="SetAdditionalPropertiesAsync"/></para>
    /// </summary>
    protected abstract IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties { get; }

    /// <summary>
    /// Based on: <see cref="InsertCommand{TModelIn, TModelOut, TEntity, IContext}.Execute(TModelIn)"/>
    /// </summary>
    public async Task<IOperationResult<TModel>> ExecuteAsync(TPatchModel patchModel)
    {
        try
        {
            // Sanity checks TODO: Convert this to a reflection-based unit test with EVEN MORE checks! :)
            if (!typeof(IYearEntity).IsAssignableFrom(typeof(TEntity)) && !typeof(IPayrollPeriodEntity).IsAssignableFrom(typeof(TEntity)))
            {
                throw new InvalidOperationException(
                    $"Entity is neither {nameof(IYearEntity)} or {nameof(IPayrollPeriodEntity)}: {typeof(TEntity).Name}");
            }
            if (!typeof(IYearEntity).IsAssignableFrom(typeof(TModelEntity)) && !typeof(IPayrollPeriodEntity).IsAssignableFrom(typeof(TModelEntity)))
            {
                throw new InvalidOperationException(
                    $"Model entity is neither {nameof(IYearEntity)} or {nameof(IPayrollPeriodEntity)}: {typeof(TModelEntity).Name}");
            }

            // Execute validation logic like regular commands
            await BeforeValidationAsync(patchModel);
            var validationMessages = await ExecuteValidationRules(patchModel, null);
            if (validationMessages.Any(x => x.MessageCode == ValidationConstants.INFO_ValidateOnly
                || (x.MessageType != MessageTypeEnum.Info && x.MessageType != MessageTypeEnum.Warning)))
            {
                return new OperationResult<TModel>(validationMessages);
            }
            await AfterValidationAsync(patchModel);

            // Get entity on current inheritance level (CLA / WM / PA)
            var entityOnCurrentLevel = await this.dbContext.Set<TEntity>()
                .Include(x => x.InheritanceLevel).ThenInclude(il => il.ParentInheritanceLevel)
                .Where(GeneratedIdHelper.ConstructWhere<TEntity>(patchModel.Id))
                .SingleOrDefaultAsync();
            // Not found message like regular commands
            if (entityOnCurrentLevel == null)
                return new OperationResult<TModel>(
                    new OperationMessage(MessageTypeEnum.NotFound, MessageCodes.API_NotFound, $"{typeof(TEntity)} not found"));

            // Get entity on parent inheritance level (CLA / WM)
            TEntity? entityOnParentLevel = null;
            if (entityOnCurrentLevel != null && entityOnCurrentLevel.InheritanceLevel.ParentInheritanceLevel != null)
            {
                var entry = this.dbContext.DbContext.Entry(entityOnCurrentLevel);
                var primaryKey = entry.Metadata.FindPrimaryKey() ?? throw new InvalidOperationException($"Primary key not found on entity: {typeof(TEntity)}");
                entityOnParentLevel = new()
                {
                    InheritanceLevelId = entityOnCurrentLevel.InheritanceLevel.ParentInheritanceLevel.InheritanceLevelId,
                };
                foreach (var property in primaryKey.Properties)
                {
                    if (property.Name == nameof(entityOnCurrentLevel.InheritanceLevelId)) continue;

                    var key = (int)entry.Property(property).CurrentValue!;
                    entityOnParentLevel.GetType().GetProperty(property.Name)!.SetValue(entityOnParentLevel, key);
                }
                entityOnParentLevel = await this.dbContext.Set<TEntity>()
                    .Where(GeneratedIdHelper.ConstructWhere<TEntity>(entityOnParentLevel.Id))
                    .SingleOrDefaultAsync();
            }

            // Map patch model to desired entity
            var entityFromPatchModel = this.mapper.Map<TEntity>(patchModel);
            entityFromPatchModel.Id = patchModel.Id;
            GeneratedIdHelper.GenerateIdKeys(entityFromPatchModel);

            // Determine which properties are different from the parent level
            var valuesOnCurrentLevel = new Dictionary<PropertyInfo, object?>();
            foreach (var property in this.Properties)
            {
                var desiredValue = property.Entity.GetValue(entityFromPatchModel)!;
                var parentValue = entityOnParentLevel == null ? null : property.Entity.GetValue(entityOnParentLevel);
                if (ValuesAreEqual(desiredValue, parentValue))
                {
                    valuesOnCurrentLevel.Add(property.ModelEntity, null);
                }
                else
                {
                    valuesOnCurrentLevel.Add(property.ModelEntity, desiredValue);
                }
            }

            // Get existing model entity on current inheritance level (CLA / WM / PA)
            var modelEntityOnCurrentLevel = await this.dbContext.Set<TModelEntity>()
                .Where(GeneratedIdHelper.ConstructWhere<TModelEntity>(patchModel.Id))
                .SingleOrDefaultAsync();

            // If an entity is update-only, we're not allowed to CREATE or DELETE model records, just UPDATE values on existing model records.
            if (this.UpdateOnly)
            {
                if (modelEntityOnCurrentLevel != null)
                {
                    // UPDATE: Model entity exists and there are values to override. Modify model entity by overwriting existing values.
                    foreach (var kvp in valuesOnCurrentLevel)
                    {
                        kvp.Key.SetValue(modelEntityOnCurrentLevel, kvp.Value);
                    }
                }
            }
            else if (valuesOnCurrentLevel.Values.Any(x => x != null))
            {
                if (modelEntityOnCurrentLevel == null)
                {
                    // CREATE: Model entity does not exist, but there are values to override. Add a new model entity.
                    modelEntityOnCurrentLevel = this.mapper.Map<TModelEntity>(entityFromPatchModel);
                    foreach (var kvp in valuesOnCurrentLevel)
                    {
                        kvp.Key.SetValue(modelEntityOnCurrentLevel, kvp.Value);
                    }

                    this.dbContext.Set<TModelEntity>().Add(modelEntityOnCurrentLevel);
                }
                else
                {
                    // UPDATE: Model entity exists and there are values to override. Modify model entity by overwriting existing values.
                    foreach (var kvp in valuesOnCurrentLevel)
                    {
                        kvp.Key.SetValue(modelEntityOnCurrentLevel, kvp.Value);
                    }
                }
            }
            else if (modelEntityOnCurrentLevel != null)
            {
                // DELETE: Model entity still exists, but there's nothing left to override. Therefore it's obsolete, delete it.
                this.dbContext.Set<TModelEntity>().Remove(modelEntityOnCurrentLevel);
            }

            await SetAdditionalPropertiesAsync(patchModel, modelEntityOnCurrentLevel);

            // Only create audit trail if model entity was created, modified, or deleted.
            IEnumerable<AuditTrailRecord>? auditTrailRecords = null;
            if (modelEntityOnCurrentLevel != null)
            {
                auditTrailRecords = CreateAuditTrail(modelEntityOnCurrentLevel);
            }
            await this.dbContext.SaveChangesAsync();
            if (auditTrailRecords != null)
            {
                await ProcessAuditTrailAsync(auditTrailRecords);
            }

            var result = await this.query.ExecuteAsync(patchModel.Id);

            // Add validation messages to the result
            foreach (var message in validationMessages)
            {
                result.Messages.Add(message);
            }

            return result;
        }
        // Handle exceptions like regular commands
        catch (Exception ex)
        {
            return HandleException(ex);
        }
    }

    protected virtual Task SetAdditionalPropertiesAsync(TPatchModel patchModel, TModelEntity? modelEntityOnCurrentLevel) => Task.CompletedTask;

    private static bool ValuesAreEqual(object? left, object? right)
    {
        if (left == null)
            return right == null;
        else if (right == null)
            return false;
        else if (left.GetType() != right.GetType())
            return false;
        else
        {
            return left switch // All inheritance entity properties are one of these types:
            {
                string leftString => leftString == (string)right,
                int leftInt => leftInt == (int)right,
                decimal leftDecimal => leftDecimal == (decimal)right,
                _ => throw new NotImplementedException($"Unhandled type: {left.GetType()}"),
            };
        }
    }
}