using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Attributes;

/// <summary>
/// Variant of <see cref="AuthorizeEntityAttribute{T}"/> that chooses between activity IDs based on the <see cref="InheritanceLevel"/> of the resource.
/// <para><see cref="InheritanceLevel.CollectiveLaborAgreement"/> -> <see cref="CollectiveLaborAgreementActivityId"/></para>
/// <para><see cref="InheritanceLevel.WageModel"/> -> <see cref="WageModelActivityId"/></para>
/// <para><see cref="InheritanceLevel.PayrollAdministration"/> -> <see cref="PayrollAdministrationActivityId"/></para>
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = true, Inherited = false)]
public class AuthorizeInheritanceLevelEntityAttribute<T>(string? collectiveLaborAgreementActivityId, string? wageModelActivityId, string? payrollAdministrationActivityId)
    : AuthorizeEntityAttribute<T>(Guid.Empty.ToString()) // This activity ID is irrelevant because we always choose one of the 3 given above
{
    /// <summary>
    /// Id of the <see cref="InheritanceLevel.CollectiveLaborAgreement"/> activity on which the attribute will authorize
    /// </summary>
    public Guid? CollectiveLaborAgreementActivityId { get; } = collectiveLaborAgreementActivityId != null ? Guid.Parse(collectiveLaborAgreementActivityId) : null;

    /// <summary>
    /// Id of the <see cref="InheritanceLevel.WageModel"/> activity on which the attribute will authorize
    /// </summary>
    public Guid? WageModelActivityId { get; } = wageModelActivityId != null ? Guid.Parse(wageModelActivityId) : null;

    /// <summary>
    /// Id of the <see cref="InheritanceLevel.PayrollAdministration"/> activity on which the attribute will authorize
    /// </summary>
    public Guid? PayrollAdministrationActivityId { get; } = payrollAdministrationActivityId != null ? Guid.Parse(payrollAdministrationActivityId) : null;

    protected override Guid GetActivityId(InheritanceLevel? inheritanceLevel)
    {
        if (!inheritanceLevel.HasValue)
        {
            throw new UnauthorizedAccessException($"Insufficient rights for this request, reason: Unknown inheritance level.");
        }
        else
        {
            switch (inheritanceLevel.Value)
            {
                case InheritanceLevel.CollectiveLaborAgreement:
                    if (this.CollectiveLaborAgreementActivityId.HasValue) return this.CollectiveLaborAgreementActivityId.Value;
                    break;
                case InheritanceLevel.WageModel:
                    if (this.WageModelActivityId.HasValue) return this.WageModelActivityId.Value;
                    break;
                case InheritanceLevel.PayrollAdministration:
                    if (this.PayrollAdministrationActivityId.HasValue) return this.PayrollAdministrationActivityId.Value;
                    break;
            }
            throw new UnauthorizedAccessException($"Insufficient rights for this request, reason: Blocked inheritance level.");
        }
    }
}