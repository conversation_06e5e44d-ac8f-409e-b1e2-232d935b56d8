using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Infrastructure.Interfaces;

public interface IInsertInheritanceEntityCommand<TPostModel, TModel, TEntity, TModelEntity>
    where TPostModel : class
    where TModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
    where TModelEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    bool IsAddFutureYearsChildCommand { get; set; }
    bool IsAddArrangementPayrollComponentsChildCommand { get; set; }

    Task<IOperationResult<TModel>> ExecuteAsync(TPostModel postModel);
}