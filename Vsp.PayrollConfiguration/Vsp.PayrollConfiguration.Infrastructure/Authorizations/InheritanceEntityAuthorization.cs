using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Authorizations;

/// <summary>
/// Variant of <see cref="AuthorizeLoketBase{T}"/> that authorizes on each <see cref="InheritanceLevel"/> of a <see cref="IInheritanceEntity"/>.
/// <para>The given <see cref="GeneratedIdEntity.Id"/> is queried in the database to automatically determine the correct <see cref="InheritanceLevel"/>.</para>
/// </summary>
public abstract class InheritanceEntityAuthorization<TAuthorizationModel, TEntity>(ILoketContext loketContext)
    : AuthorizeLoketBase<TAuthorizationModel>
    where TAuthorizationModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    private readonly ILoketContext loketContext = loketContext;

    public override Task<(ResourceType ResourceType, Guid EntityId)> AuthorizeLoketEntity(ICurrentContext currentContext, TAuthorizationModel authorizationObject)
    {
        var resourceType = this.ResourceType;
        var id = GetId(authorizationObject);

        return Task.FromResult((resourceType, id));
    }

    public override async Task<InheritanceLevel?> AuthorizeLoketInheritanceLevel(ICurrentContext currentContext, TAuthorizationModel authorizationObject)
    {
        var id = GetId(authorizationObject);

        var entityLevelType = await this.loketContext.Set<TEntity>()
            .Where(GeneratedIdHelper.ConstructWhere<TEntity>(id))
            .Select(x => x.InheritanceLevel.InheritanceLevelInfo.Type)
            .SingleOrDefaultAsync();

        InheritanceLevel? inheritanceLevel = entityLevelType switch
        {
            (int)InheritanceLevel.CollectiveLaborAgreement => InheritanceLevel.CollectiveLaborAgreement,
            (int)InheritanceLevel.WageModel => InheritanceLevel.WageModel,
            (int)InheritanceLevel.PayrollAdministration => InheritanceLevel.PayrollAdministration,
            0 => null, // NOTE: In case of a non-existing entity - we don't throw an exception because the endpoint would return 500 instead of 403.
            _ => throw new NotImplementedException($"Unhandled inheritance level: {entityLevelType}")
        };

        return inheritanceLevel;
    }

    protected abstract ResourceType ResourceType { get; }

    protected abstract Guid GetId(TAuthorizationModel authorizationObject);
}
