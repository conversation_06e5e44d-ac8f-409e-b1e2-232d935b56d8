namespace Vsp.PayrollConfiguration.Year.Constants;

public static class YearRoutes
{
    public const string GetYearsMinimizedByInheritanceLevelIdAsync = "years/minimized";
    public const string GetYearsByCollectionLaborAgreementIdAsync = "collectivelaboragreements/{collectiveLaborAgreementId:guid}/years";
    public const string GetYearsByWageModelIdAsync = "wagemodels/{wageModelId:guid}/years";
    public const string GetYearsByPayrollAdministrationIdAsync = "payrolladministrations/{payrollAdministrationId:guid}/years";
    public const string PatchYearByWageModelYearIdAsync = "wagemodels/years/{yearId:guid}";
    public const string PatchYearByPayrollAdministrationYearIdAsync = "payrolladministrations/years/{yearId:guid}";
    public const string GetYearMetadataByWageModelYearIdAsync = "wagemodels/years/{yearId:guid}/metadata";
    public const string GetYearMetadataByPayrollAdministrationYearIdAsync = "payrolladministrations/years/{yearId:guid}/metadata";
    public const string GetPayrollPeriodsByYearIdAsync = "years/{yearId:guid}/payrollperiods";
}