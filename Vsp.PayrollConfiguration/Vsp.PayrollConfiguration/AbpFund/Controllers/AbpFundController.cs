using System.Net;
using Vsp.PayrollConfiguration.AbpFund.Constants;
using Vsp.PayrollConfiguration.Domain.AbpFund.Authorizations;
using Vsp.PayrollConfiguration.Domain.AbpFund.Interfaces;
using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.AbpFund.Controllers;

/// <summary>
/// ABP Fund related endpoints (NL: Fonds ABP)
/// </summary>
[Tags("ABP Fund")]
[Authorize]
[ApiController]
public class AbpFundController(IResultHandler resultHandler, IAbpFundService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IAbpFundService service = service;

    /// <summary>
    /// List of ABP funds
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///   - <c>GetAbpFundsByCollectiveLaborAgreementYearId</c>
    ///   - <c>GetAbpFundsByWageModelYearId</c>
    ///   - <c>GetAbpFundsByPayrollAdministrationYearId</c><br/>
    /// Retrieves the list of defined ABP funds on the given year of an inheritance level (CLA vs WM vs PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(AbpFundRoutes.GetAbpFundsByYearIdAsync)]
    [AuthorizeInheritanceLevelEntity<YearAuthorizationModel>(
        "15d58e7a-00cb-4a51-98e2-373b4ba34cf5", // GetAbpFundsByCollectiveLaborAgreementYearId
        "25890a80-5cb2-4f52-9c48-0a9a3e21d9c6", // GetAbpFundsByWageModelYearId
        "05047264-88c5-4293-b904-24734d52cfea"  // GetAbpFundsByPayrollAdministrationYearId
    )]
    public async Task<ActionResult<ListResult<AbpFundModel>>> GetAbpFundsByYearIdAsync([FromRoute] Guid yearId)
    {
        var result = await this.service.GetAbpFundsByYearIdAsync(yearId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Adds a new ABP fund
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///   - <c>PostAbpFundByCollectiveLaborAgreementId</c>
    ///   - <c>PostAbpFundByWageModelId</c>
    ///   - <c>PostAbpFundByPayrollAdministrationId</c><br/>
    /// Adds a new ABP fund on the given inheritance level (CLA vs WM vs PA).
    /// **Validation errors**:
    ///   - <c>API_PayrollConfiguration_AbpFund_Key_Invalid</c>key is invalid
    /// </remarks>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Post))]
    [HttpPost]
    [Route(AbpFundRoutes.PostAbpFundByInheritanceLevelIdAsync)]
    [AuthorizeInheritanceLevelEntity<InheritanceLevelAuthorizationModel>(
        "3b38155e-78de-4167-bb4c-95d696f8382c", // PostAbpFundByCollectiveLaborAgreementId
        "2ce7328f-7958-488e-8f29-6e68528b1702", // PostAbpFundByWageModelId
        "7df90afe-2e2c-48fb-9d01-5ea4f28beb42"  // PostAbpFundByPayrollAdministrationId
    )]
    public async Task<ActionResult<DetailResult<AbpFundModel>>> PostAbpFundByInheritanceLevelIdAsync(
        [FromQuery] [ExactlyOneIdRequired] InheritanceLevelQuerystringParams querystringParams,
        [Required(ErrorMessage = "POST payload is required.")] AbpFundPostModel postModel)
    {
        var result = await this.service.PostAbpFundByInheritanceLevelIdAsync(querystringParams.GetId(), postModel);
        return this.resultHandler.ToTypedActionResult(result, (int)HttpStatusCode.Created);
    }

    /// <summary>
    /// Edit an ABP fund
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///   - <c>PatchAbpFundByCollectiveLaborAgreementAbpFundId</c>
    ///   - <c>PatchAbpFundByWageModelAbpFundId</c>
    ///   - <c>PatchAbpFundByPayrollAdministrationAbpFundId</c><br/>
    /// Modifies an existing ABP fund with the given id – defined on an inheritance level (CLA vs WM vs PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(AbpFundRoutes.PatchAbpFundByAbpFundIdAsync)]
    [AuthorizeInheritanceLevelEntity<AbpFundAuthorizationModel>(
        "5e688702-491b-43fc-880e-17cc54a23ce2", // PatchAbpFundByCollectiveLaborAgreementAbpFundId
        "f7948ac3-80bb-42a0-8488-a640ea79136c", // PatchAbpFundByWageModelAbpFundId
        "91b4a9af-80c0-40db-8a0d-4fdf699dd96d"  // PatchAbpFundByPayrollAdministrationAbpFundId
    )]
    public async Task<ActionResult<DetailResult<AbpFundModel>>> PatchAbpFundByAbpFundIdAsync(
        [FromRoute] Guid abpFundId,
        [Required(ErrorMessage = "PATCH payload is required.")] AbpFundPatchModel patchModel)
    {
        var result = await this.service.PatchAbpFundByAbpFundIdAsync(abpFundId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Deletes an existing ABP fund
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///   - <c>DeleteAbpFundByCollectiveLaborAgreementAbpFundId</c>
    ///   - <c>DeleteAbpFundByWageModelAbpFundId</c>
    ///   - <c>DeleteAbpFundByPayrollAdministrationAbpFundId</c><br/>
    /// Deletes an existing ABP fund with the given id – defined on an inheritance level (CLA vs WM vs PA).
    /// **Validation errors**:
    ///   - <c>API_PayrollConfiguration_AbpFund_Delete_InUse_EmploymentProfile</c>ABP fund is used in: employment profile  
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Delete))]
    [HttpDelete]
    [Route(AbpFundRoutes.DeleteAbpFundByAbpFundIdAsync)]
    [AuthorizeInheritanceLevelEntity<AbpFundAuthorizationModel>(
        "ecd4de72-d5ca-4082-9452-4b2566160e4f", // DeleteAbpFundByCollectiveLaborAgreementAbpFundId
        "404247b9-7e0c-4e51-92dd-c1d0f9f4281d", // DeleteAbpFundByWageModelAbpFundId
        "09de95a0-831a-4219-b9ee-abea64ee732b"  // DeleteAbpFundByPayrollAdministrationAbpFundId
    )]
    public async Task<ActionResult<DetailResult<NoResult>>> DeleteAbpFundByAbpFundIdAsync([FromRoute] Guid abpFundId)
    {
        var result = await this.service.DeleteAbpFundByAbpFundIdAsync(abpFundId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for ABP fund per provider
    /// </summary>
    /// <remarks>
    /// **Activity name**:
    ///    - <c>GetAbpFundMetadataByProviderId</c><br/>
    /// Retrieves the list of available options for POST and PATCH.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(AbpFundRoutes.GetAbpFundMetadataByProviderIdAsync)]
    [AuthorizeEntity<ProviderAuthorizationModel>("e1f166bf-193b-4c02-9bc3-aea80c0403b6")]
    public async Task<ActionResult<DetailResult<AbpFundMetadataModel>>> GetAbpFundMetadataByProviderIdAsync([FromRoute] Guid providerId)
    {
        var result = await this.service.GetAbpFundMetadataByProviderIdAsync();
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of payroll components linked to an ABP fund
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///   - <c>GetLinkedPayrollComponentsByCollectiveLaborAgreementAbpFundId</c>
    ///   - <c>GetLinkedPayrollComponentsByWageModelAbpFundId</c>
    ///   - <c>GetLinkedPayrollComponentsByPayrollAdministrationAbpFundId</c><br/>
    /// Retrieves the list of linked payroll components for the given ABP fund – defined on an inheritance level (CLA vs WM vs PA).
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(AbpFundRoutes.GetLinkedPayrollComponentsByAbpFundId)]
    [AuthorizeInheritanceLevelEntity<AbpFundAuthorizationModel>(
        "98a0ee96-f262-43ae-bc17-11ebc881688d", // GetLinkedPayrollComponentsByCollectiveLaborAgreementAbpFundId
        "00e233f8-ebf9-46a6-997d-9e353d208915", // GetLinkedPayrollComponentsByWageModelAbpFundId
        "72725dd5-c95e-49e1-97cb-f66ea9831f7d"  // GetLinkedPayrollComponentsByPayrollAdministrationAbpFundId
    )]
    public async Task<ActionResult<ListResult<PayrollComponentModel>>> GetLinkedPayrollComponentsByAbpFundIdAsync([FromRoute] Guid abpFundId)
    {
        var result = await this.service.GetLinkedPayrollComponentsByAbpFundIdAsync(abpFundId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}
