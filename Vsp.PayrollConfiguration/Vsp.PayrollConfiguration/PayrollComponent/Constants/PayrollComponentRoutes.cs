namespace Vsp.PayrollConfiguration.PayrollComponent.Constants;

public static class PayrollComponentRoutes
{
    public const string GetPayrollComponentsByYearIdAsync = "years/{yearId:guid}/payrollcomponents";
    public const string GetPayrollComponentsMinimizedByYearIdAsync = "years/{yearId:guid}/payrollcomponents/minimized";
    public const string GetAvailablePayrollComponentsByYearIdAsync = "years/{yearId:guid}/payrollcomponents/available";
    public const string PostPayrollComponentByInheritanceLevelIdAsync = "payrollcomponents";
    public const string PatchPayrollComponentByPayrollComponentIdAsync = "payrollcomponents/{payrollComponentId:guid}";
    public const string DeletePayrollComponentByPayrollComponentIdAsync = "payrollcomponents/{payrollComponentId:guid}";
    public const string GetPayrollComponentMetadataByProviderIdAsync = "providers/{providerId:guid}/payrollcomponents/metadata";
}