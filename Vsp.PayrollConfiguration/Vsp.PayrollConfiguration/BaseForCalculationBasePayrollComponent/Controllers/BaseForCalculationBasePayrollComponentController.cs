using System.Net;
using Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Constants;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;

namespace Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Controllers;

/// <summary>
/// Base For Calculation Base Payroll Component related endpoints (NL: Grondslag Basis)
/// </summary>
[Tags("Base For Calculation Base Payroll Component")]
[Authorize]
[ApiController]
public class BaseForCalculationBasePayrollComponentController(
    IResultHandler resultHandler,
    IBaseForCalculationBasePayrollComponentService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IBaseForCalculationBasePayrollComponentService service = service;

    /// <summary>
    /// List of base for calculation base payroll components
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetBaseForCalculationBasePayrollComponentsByCollectiveLaborAgreementBaseForCalculationId</c>
    ///    - <c>GetBaseForCalculationBasePayrollComponentsByWageModelBaseForCalculationId</c>
    ///    - <c>GetBaseForCalculationBasePayrollComponentsByPayrollAdministrationBaseForCalculationId</c><br/>
    ///  Retrieves the list of defined base payroll components for the given base for calculation.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(BaseForCalculationBasePayrollComponentRoutes.GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "902334ec-f5e0-4ae2-9c08-73276c7f5def", // GetBaseForCalculationBasePayrollComponentsByCollectiveLaborAgreementBaseForCalculationId
        "cf6f5ccb-da62-4801-8152-4f4ea39f8a80", // GetBaseForCalculationBasePayrollComponentsByWageModelBaseForCalculationId
        "db6d796d-4cf9-47ee-aa7a-4963a5b3dbcb")] // GetBaseForCalculationBasePayrollComponentsByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<ListResult<BaseForCalculationBasePayrollComponentModel>>> GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync(
        [FromRoute] Guid baseForCalculationId)
    {
        var result = await this.service.GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync(baseForCalculationId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Adds a new base for calculation base payroll component
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PostBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationId</c>
    ///    - <c>PostBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationId</c>
    ///    - <c>PostBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationId</c><br/>
    /// Adds a new base payroll component for the given base for calculation<br/>
    /// **Validation errors**: TODO!
    /// </remarks>
    [Consumes(MediaTypeNames.Application.Json)]
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Post))]
    [HttpPost]
    [Route(BaseForCalculationBasePayrollComponentRoutes.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "8ead66a3-0873-46f9-b1df-77eba90775c7", // PostBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationId
        "af8ce5ed-548c-49bf-ae20-67ed2e8e2b84", // PostBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationId
        "5df24722-3994-4ae1-8804-f2385f347e8c")] // PostBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationId
    public async Task<ActionResult<DetailResult<BaseForCalculationBasePayrollComponentModel>>> PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync(
        [FromRoute] Guid baseForCalculationId,
        [FromBody] BaseForCalculationBasePayrollComponentPostModel postModel)
    {
        var result = await this.service.PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync(baseForCalculationId, postModel);
        return this.resultHandler.ToTypedActionResult(result, (int)HttpStatusCode.Created);
    }

    /// <summary>
    /// Edit a base for calculation base payroll component
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationBasePayrollComponentId</c>
    ///    - <c>PatchBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationBasePayrollComponentId</c>
    ///    - <c>PatchBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationBasePayrollComponentId</c><br/>
    /// Edit an existing base payroll component for the given base for calculation<br/>
    /// **Validation errors**:
    ///    - <c>API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid</c> - origin.key is invalid
    ///    - <c>API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_1 </c> - Net-to-gross components from employee data may not be included in a base for calculation. 
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Patch))]
    [HttpPatch]
    [Route(BaseForCalculationBasePayrollComponentRoutes.PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationBasePayrollComponentAuthorizationModel>(
        "69796ea4-f8b4-4f4b-bff7-4994924c8ec3", // PatchBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationBasePayrollComponentId
        "41dea1f1-4c0a-4779-9102-217168fd0574", // PatchBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationBasePayrollComponentId
        "2c37803a-2ae0-4634-9084-74a58422e4f7")] // PatchBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationBasePayrollComponentId
    public async Task<ActionResult<DetailResult<BaseForCalculationBasePayrollComponentModel>>> PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(
        [FromRoute] Guid baseForCalculationBasePayrollComponentId,
        [FromBody] BaseForCalculationBasePayrollComponentPatchModel patchModel)
    {
        var result = await this.service.PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(baseForCalculationBasePayrollComponentId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Deletes an existing base for calculation base payroll component
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>DeleteBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationBasePayrollComponentId</c>
    ///    - <c>DeleteBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationBasePayrollComponentId</c>
    ///    - <c>DeleteBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationBasePayrollComponentId</c><br/>
    /// Deletes an existing base payroll component for the given base for calculation<br/>
    /// **Validation errors**: TODO!
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.Delete))]
    [HttpDelete]
    [Route(BaseForCalculationBasePayrollComponentRoutes.DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationBasePayrollComponentAuthorizationModel>(
        "4096ba70-a30a-4cbd-a097-9cafbc0c52de", // DeleteBaseForCalculationBasePayrollComponentByCollectiveLaborAgreementBaseForCalculationBasePayrollComponentId
        "3bf2966b-33a2-46a4-a057-00813bff7025", // DeleteBaseForCalculationBasePayrollComponentByWageModelBaseForCalculationBasePayrollComponentId
        "3924053b-6a9f-4fce-957e-784810f3dfa0")] // DeleteBaseForCalculationBasePayrollComponentByPayrollAdministrationBaseForCalculationBasePayrollComponentId
    public async Task<ActionResult<DetailResult<NoResult>>> DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(
        [FromRoute] Guid baseForCalculationBasePayrollComponentId)
    {
        var result = await this.service.DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(baseForCalculationBasePayrollComponentId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Metadata for base for calculation base payroll component
    /// </summary>
    /// <remarks>
    /// 
    /// **Activity name**: <c>GetBaseForCalculationBasePayrollComponentMetadataByProviderId</c>
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(BaseForCalculationBasePayrollComponentRoutes.GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync)]
    [AuthorizeEntity<ProviderAuthorizationModel>("bfdd9b84-5917-4afc-be29-ac7ea22076bb")]
    public async Task<ActionResult<DetailResult<BaseForCalculationBasePayrollComponentMetadataModel>>> GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync(
        [FromRoute] Guid providerId)
    {
        var result = await this.service.GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync(providerId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// List of available base payroll components
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetAvailableBasePayrollComponentsByCollectiveLaborAgreementBaseForCalculationIdAndPayrollPeriodNumber</c>
    ///    - <c>GetAvailableBasePayrollComponentsByWageModelBaseForCalculationIdAndPayrollPeriodNumber</c>
    ///    - <c>GetAvailableBasePayrollComponentsByPayrollAdministrationBaseForCalculationIdAndPayrollPeriodNumber</c><br/>
    /// Retrieves the list of available base payroll components for base for calculation
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(BaseForCalculationBasePayrollComponentRoutes.GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync)]
    [AuthorizeInheritanceLevelEntity<BaseForCalculationAuthorizationModel>(
        "0e910004-4c3a-46d3-aff5-823384f46b72", // GetAvailableBasePayrollComponentsByCollectiveLaborAgreementBaseForCalculationIdAndPayrollPeriodNumber
        "8eaff106-6d9d-4fad-a0ab-5a9d87a95f4f", // GetAvailableBasePayrollComponentsByWageModelBaseForCalculationIdAndPayrollPeriodNumber
        "58bd4d8d-d958-4c07-8412-4d7db2dabbf4")] // GetAvailableBasePayrollComponentsByPayrollAdministrationBaseForCalculationIdAndPayrollPeriodNumber
    public async Task<ActionResult<ListResult<PayrollComponentMinimizedModel>>> GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync(
        [FromRoute] Guid baseForCalculationId,
        [FromRoute] int payrollPeriodNumber)
    {
        var result = await this.service.GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync(baseForCalculationId, payrollPeriodNumber);
        return this.resultHandler.ToTypedActionResult(result);
    }
}