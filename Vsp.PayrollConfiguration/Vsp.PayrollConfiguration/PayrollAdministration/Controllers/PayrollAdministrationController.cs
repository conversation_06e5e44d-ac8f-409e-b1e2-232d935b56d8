using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Authorizations;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Authorizations;
using Vsp.PayrollConfiguration.PayrollAdministration.Constants;

namespace Vsp.PayrollConfiguration.PayrollAdministration.Controllers;

/// <summary>
/// Payroll administration (PA) related endpoints (NL: administratie)
/// </summary>
[Tags("Payroll Administration (PA)")]
[Authorize]
[ApiController]
public class PayrollAdministrationController(IResultHandler resultHandler, IPayrollAdministrationService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IPayrollAdministrationService service = service;

    /// <summary>
    /// List of payroll administrations
    /// </summary>
    /// <remarks>
    /// **Activity name**: <c>GetUlsaPayrollAdministrationsByBearerToken</c><br/>
    /// Retrieves a list of payroll administrations for the current logged-in user.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(PayrollAdministrationRoutes.GetPayrollAdministrationsByBearerTokenAsync)]
    [AuthorizeEntity<UserAuthorizationModel>("ec8b9105-a51c-4758-b97b-a232bed25428")] // GetUlsaPayrollAdministrationsByBearerToken
    public async Task<ActionResult<ListResult<PayrollAdministrationModel>>> GetPayrollAdministrationsByBearerTokenAsync()
    {
        var result = await this.service.GetPayrollAdministrationsByBearerTokenAsync();
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Details of a payroll administration
    /// </summary>
    /// <remarks>
    /// **Activity name**: <c>GetUlsaPayrollAdministrationByPayrollAdministrationId</c><br/>
    /// Retrieves details of a payroll administration.
    /// </remarks>
    /// <param name="payrollAdministrationId" example="123e4567-e89b-12d3-a456-************">The unique identifier of a payroll administration (GUID/UUID).</param>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(PayrollAdministrationRoutes.GetPayrollAdministrationByPayrollAdministrationIdAsync)]
    [AuthorizeEntity<PayrollAdministrationAuthorizationModel>("365a1412-4e33-4163-b87d-041a79517bda")] // GetUlsaPayrollAdministrationByPayrollAdministrationId
    public async Task<ActionResult<DetailResult<PayrollAdministrationModel>>> GetPayrollAdministrationByPayrollAdministrationIdAsync(Guid payrollAdministrationId)
    {
        var result = await this.service.GetPayrollAdministrationByPayrollAdministrationIdAsync(payrollAdministrationId);
        return this.resultHandler.ToTypedActionResult(result);
    }
}
