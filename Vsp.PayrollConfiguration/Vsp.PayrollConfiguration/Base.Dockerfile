# Sdk alpine als build base
FROM mcr.microsoft.com/dotnet/sdk:9.0-alpine AS build

# The folder of the solution e.g. Vsp.Werkkosten.Core
ARG SOLUTION_FOLDER

# Globalization toevoegen, niet standaard op alpine, nodig voor oa. EF core
RUN apk add icu-data-full icu-libs tzdata libsecret
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
# Set the locale
ENV LC_ALL=nl_NL.UTF-8
ENV LANG=nl_NL.UTF-8
ENV TZ="Europe/Amsterdam"

# Loket.nl Nuget feed toevoegen
RUN --mount=type=secret,id=NUGET_TOKEN,env=NUGET_TOKEN \
    dotnet nuget add source https://pkgs.dev.azure.com/loket/_packaging/Loket.nl/nuget/v3/index.json \
      -n Loket \
      --username maaktnietuit \
      --password ${NUGET_TOKEN} \
      --store-password-in-clear-text

# Copy all source
WORKDIR /src
COPY . .

# Restore met linux musl target, optimaliseert voor oa. Alpine
RUN dotnet restore "$SOLUTION_FOLDER.sln" --runtime linux-musl-x64
RUN dotnet build "$SOLUTION_FOLDER.sln" -c Release --no-restore