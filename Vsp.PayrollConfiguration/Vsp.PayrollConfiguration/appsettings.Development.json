{"ApplicationInsights": {"ConnectionString": "InstrumentationKey=2d4dc74b-aa93-4a20-a73c-c9ebd483af5f;IngestionEndpoint=https://westeurope-5.in.applicationinsights.azure.com/;LiveEndpoint=https://westeurope.livediagnostics.monitor.azure.com/"}, "ConnectionStrings": {"SettingsDatabase": "Server=sqlmi-weu-dev-001.4dd48adfb4b8.database.windows.net,1433;Initial Catalog=settings;Persist Security Info=False;User ID=UserSettings; Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;", "Vsp.GlobalFilter.Core.InternalService.BaseUri": "http://k8s.vsp.local/globalfiltercore/"}, "ApiBase": {"AuthenticationServiceUrl": "https://oauth.loket-ontw.nl/", "AuthorizationServiceUrl": "http://k8s.vsp.local/authorizationcore/", "ServiceBus": "Endpoint=sb://sb-weu-dev-001.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=UCFTKh+aj3vTITQzrbLva23iCLInfDZ0C+u+XEeWU2w=", "RedisCache": "redis-weu-dev.redis.cache.windows.net:6380,password=YQ8bawubsnb6ybWySMr1PyWUevqRDVRdVAzCaFGd9eI=,ssl=True,abortConnect=False", "AICoreServiceUrl": "http://k8s.vsp.local/aicore/", "KeyVault": {"Url": "https://kv-weu-dev-001.vault.azure.net/", "K8SClientId": "b923db90-1ddc-46d0-b593-ae4b6aaafb3b"}}, "MachineKey": {"DecryptionKey": "FD310B6CC46D500FB73FCD125FCC5B23613A15B8C69CAAF31F1AF9E1A5966EC4", "ValidationKey": "5F183D586B84765986DEE35C19BDF15FF4E458D933E78D6C347D7BEA91D54B05C0E0BCB0B940D95FD4F3A5467CE67EE222FD4CA1A80803DD5D0F7B4E4D0C1517"}}