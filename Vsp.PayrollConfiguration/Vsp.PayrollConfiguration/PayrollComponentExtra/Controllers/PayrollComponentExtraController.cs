using Vsp.PayrollConfiguration.Domain.PayrollComponent.Authorizations;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;
using Vsp.PayrollConfiguration.Infrastructure.Attributes;
using Vsp.PayrollConfiguration.PayrollComponentExtra.Constants;

namespace Vsp.PayrollConfiguration.PayrollComponentExtra.Controllers;

/// <summary>
/// Payroll component extra related endpoints (NL: extra configuratie voor looncomponenten) 
/// </summary>
[Tags("Payroll Component Extra")]
[Authorize]
[ApiController]
public class PayrollComponentExtraController(IResultHandler resultHandler, IPayrollComponentExtraService service) : ControllerBase
{
    private readonly IResultHandler resultHandler = resultHandler;
    private readonly IPayrollComponentExtraService service = service;

    /// <summary>
    /// Payroll component extra
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetPayrollComponentExtraByCollectiveLaborAgreementPayrollComponentId</c>
    ///    - <c>GetPayrollComponentExtraByWageModelPayrollComponentId</c>
    ///    - <c>GetPayrollComponentExtraByPayrollAdministrationPayrollComponentId</c><br/>
    /// Retrieves payroll component extra information for the given payroll component id.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpGet]
    [Route(PayrollComponentExtraRoutes.GetPayrollComponentExtraByPayrollComponentIdAsync)]
    [AuthorizeInheritanceLevelEntity<PayrollComponentAuthorizationModel>(
        "2c018ba8-ef6c-4df3-a64d-9780f7e3aa4c", // GetPayrollComponentExtraByCollectiveLaborAgreementPayrollComponentId
        "f64c80a1-ebd7-485e-88dc-1487e36927ab", // GetPayrollComponentExtraByWageModelPayrollComponentId
        "def53b63-63bc-49c6-b537-ccff07c9dd09")] // GetPayrollComponentExtraByPayrollAdministrationPayrollComponentId
    public async Task<ActionResult<DetailResult<PayrollComponentExtraModel>>> GetPayrollComponentExtraByPayrollComponentIdAsync(
        [FromRoute] Guid payrollComponentId)
    {
        var result = await this.service.GetPayrollComponentExtraByPayrollComponentIdAsync(payrollComponentId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Payroll component extra metadata
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>GetPayrollComponentExtraMetadataByCollectiveLaborAgreementPayrollComponentId</c>
    ///    - <c>GetPayrollComponentExtraMetadataByWageModelPayrollComponentId</c>
    ///    - <c>GetPayrollComponentExtraMetadataByPayrollAdministrationPayrollComponentId </c><br/>
    /// Retrieves payroll component extra metadata information for the given payroll component id.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetDetail))]
    [HttpGet]
    [Route(PayrollComponentExtraRoutes.GetPayrollComponentExtraMetadataByPayrollComponentIdAsync)]
    [AuthorizeInheritanceLevelEntity<PayrollComponentAuthorizationModel>(
        "fc3bc0bb-67ba-49d2-9e0c-bffcda273801", // GetPayrollComponentExtraMetadataByCollectiveLaborAgreementPayrollComponentId 
        "05ea2814-88db-4da6-96ee-b342c369528c", // GetPayrollComponentExtraMetadataByWageModelPayrollComponentId 
        "845f802a-e9e0-4e8a-9ec5-9c8765844f40")] // GetPayrollComponentExtraMetadataByPayrollAdministrationPayrollComponentId 
    public async Task<ActionResult<DetailResult<PayrollComponentExtraMetadataModel>>> GetPayrollComponentExtraMetadataByPayrollComponentId(
        [FromRoute] Guid payrollComponentId)
    {
        var result = await this.service.GetPayrollComponentExtraMetadataByPayrollComponentIdAsync(payrollComponentId);
        return this.resultHandler.ToTypedActionResult(result);
    }

    /// <summary>
    /// Edit payroll component extra
    /// </summary>
    /// <remarks>
    /// **Activity names**:
    ///    - <c>PatchPayrollComponentExtraByCollectiveLaborAgreementPayrollComponentId</c>
    ///    - <c>PatchPayrollComponentExtraByWageModelPayrollComponentId</c>
    ///    - <c>PatchPayrollComponentExtraByPayrollAdministrationPayrollComponentId</c><br/>
    /// Edits payroll component extra information for the given payroll component id.
    /// </remarks>
    [ApiConventionMethod(typeof(CustomApiConventions), nameof(CustomApiConventions.GetList))]
    [HttpPatch]
    [Route(PayrollComponentExtraRoutes.PatchPayrollComponentExtraByPayrollComponentIdAsync)]
    [AuthorizeInheritanceLevelEntity<PayrollComponentAuthorizationModel>(
        "8a810768-a1cf-4d8f-bc39-898df83493c2", // PatchPayrollComponentExtraByCollectiveLaborAgreementPayrollComponentId
        "be023105-7c43-44ef-9679-0d68eda439f3", // PatchPayrollComponentExtraByWageModelPayrollComponentId
        "07006c5d-14ba-4296-9f1e-779ca1bbca8c")] // PatchPayrollComponentExtraByPayrollAdministrationPayrollComponentId
    public async Task<ActionResult<DetailResult<PayrollComponentExtraModel>>> PatchPayrollComponentExtraByPayrollComponentIdAsync(
        [FromRoute] Guid payrollComponentId, [Required(ErrorMessage = "PATCH payload is required.")] PayrollComponentExtraPatchModel patchModel)
    {
        var result = await this.service.PatchPayrollComponentExtraByPayrollComponentIdAsync(payrollComponentId, patchModel);
        return this.resultHandler.ToTypedActionResult(result);
    }
}