{"profiles": {"Vsp.PayrollConfiguration": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:45007;http://localhost:45008"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true, "useSSL": true}}}