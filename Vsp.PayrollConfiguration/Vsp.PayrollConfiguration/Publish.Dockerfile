# The project type, e.g. service, microservice or function.
# This is used for the container repository
ARG PROJECT_TYPE
# The name of the git repository, also used for the container repository
ARG REPOSITORY_NAME
ARG BASE_IMAGE_TAG="latest-base"
ARG CONTAINER_REGISTRY_URL="acrweushd001.azurecr.io"

FROM mcr.microsoft.com/dotnet/aspnet:9.0-alpine AS base

# Globalization toevoegen, niet standaard op alpine, nodig voor oa. EF core
RUN apk add icu-data-full icu-libs tzdata libsecret
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
# Set the locale
ENV LC_ALL=nl_NL.UTF-8
ENV LANG=nl_NL.UTF-8
ENV TZ="Europe/Amsterdam"

# Non-root user toevoegen, best practice voor containers
RUN adduser --disabled-password --gecos '' dotnetuser \
  --home /app && chown -R dotnetuser /app

# Non-root users mogen geen poorten openen onder de 1024, dus default kestrel poort (5000)
ENV ASPNETCORE_URLS=http://+:5000
EXPOSE 5000

FROM ${CONTAINER_REGISTRY_URL}/${REPOSITORY_NAME}/${PROJECT_TYPE}:${BASE_IMAGE_TAG} AS build

# The folder the project is in e.g. Vsp.Werkkosten.Core, Vsp.WorkRelatedCosts, Vsp.WorkRelatedCosts.AzureFunction
ARG PROJECT_FOLDER

# Publish
FROM build AS publish
WORKDIR /src/${PROJECT_FOLDER}
RUN dotnet publish "$PROJECT_FOLDER.csproj" -c Release -o /app/publish --no-restore --runtime linux-musl-x64 --no-self-contained

FROM base AS final

ARG PROJECT_FOLDER
ENV PROJECT_DLL="$PROJECT_FOLDER.dll"

# Copy published files
WORKDIR /app
COPY --from=publish /app/publish .

# Run as non-root user
USER dotnetuser

# Entrypoint in shell form om te runnen
ENTRYPOINT dotnet $PROJECT_DLL