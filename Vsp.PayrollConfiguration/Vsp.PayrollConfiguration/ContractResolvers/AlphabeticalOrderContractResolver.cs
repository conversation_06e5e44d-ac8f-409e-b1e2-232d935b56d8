using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Vsp.PayrollConfiguration.ContractResolvers;

public class AlphabeticalOrderContractResolver : DefaultContractResolver
{
    private readonly CamelCaseNamingStrategy camelCaseNamingStrategy = new();
    protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
    {
        var properties = base.CreateProperties(type, memberSerialization);
        return properties.OrderBy(p => p.PropertyName, StringComparer.Ordinal).ToList();
    }

    protected override string ResolvePropertyName(string propertyName) => this.camelCaseNamingStrategy.GetPropertyName(propertyName, false);
}