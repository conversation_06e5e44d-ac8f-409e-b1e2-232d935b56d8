using Vsp.ApiBase.Extensions;
using Vsp.PayrollConfiguration.ContractResolvers;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddJsonFile("configuration/K8S.json", optional: builder.Environment.IsDevelopment(), reloadOnChange: true);

builder.AddApiBase(useNewtonsoftJson: true, new AlphabeticalOrderContractResolver());
builder.AddApplicationInsights();

// NOTE: Micro services cannot check the Loket DB - that requires an environment ID (NL: omgeving) that is not available in their pipeline (yet).
var sqlCheck = false;
if (sqlCheck) builder.Services.AddHealthChecks().AddDbContextCheck<Vsp.PayrollConfiguration.Repository.LoketContext>(tags: ["SQLCheck"]);

builder.AddServiceRegistrationBundlesInAssemblyWithType<Vsp.PayrollConfiguration.Domain.ServiceRegistrationBundle>()
    .AddServiceRegistrationBundlesInAssemblyWithType<Vsp.PayrollConfiguration.Infrastructure.ServiceRegistrationBundle>()
    .AddServiceRegistrationBundlesInAssemblyWithType<Vsp.PayrollConfiguration.Repository.ServiceRegistrationBundle>();

builder.Services.AddMapper(typeof(Vsp.PayrollConfiguration.Domain.ServiceRegistrationBundle).Assembly);

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

app.UseApiBase();

await app.RunAsync();

// Needed for C# integration tests to find the Program class.
public partial class Program { }
