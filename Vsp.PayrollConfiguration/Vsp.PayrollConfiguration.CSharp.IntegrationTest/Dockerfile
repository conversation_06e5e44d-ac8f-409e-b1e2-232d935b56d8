# Use the official SQL Server image
FROM mcr.microsoft.com/mssql/server:2022-latest as base

# Set environment variables
ENV ACCEPT_EULA=Y
ENV MSSQL_SA_PASSWORD="D4t4b4s3*"
USER root

# Create missing folders
RUN mkdir /var/opt/mssql/backup
RUN mkdir /usr/src/app/

# Copy the backup file and SQL script into the container
COPY ./vspqa.bak /var/opt/mssql/backup/vspqa.bak
COPY restore.sql /usr/src/app/restore.sql
COPY restore-db.sh /usr/src/app/restore-db.sh

# Make the restore script executable
RUN chmod +x /usr/src/app/restore-db.sh

# Run the restore script
RUN /usr/src/app/restore-db.sh

# Clean files used for restoring the db
RUN rm /var/opt/mssql/backup/vspqa.bak
RUN rm /usr/src/app/restore.sql
RUN rm /usr/src/app/restore-db.sh

# Use the official SQL Server image as the final stage
FROM mcr.microsoft.com/mssql/server:2022-latest

# Copy the restored database files from the base stage
COPY --from=base /var/opt/mssql /var/opt/mssql

# Set environment variables
ENV ACCEPT_EULA=Y
ENV MSSQL_SA_PASSWORD="D4t4b4s3*"

# Expose the SQL Server port
EXPOSE 1433

# Start SQL Server
ENTRYPOINT ["/opt/mssql/bin/sqlservr"]
