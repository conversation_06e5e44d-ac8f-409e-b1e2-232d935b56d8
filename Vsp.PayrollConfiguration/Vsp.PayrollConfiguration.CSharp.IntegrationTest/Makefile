
# Makefile to automate the process of generating VspQA image

.PHONY: run_container create_missing_folders add_restore restore_db create_image everything

run_container:
	@echo Running container
	@docker run --rm -e ACCEPT_EULA=Y -e MSSQL_SA_PASSWORD=D4t4b4s3* -p 1433:1433 --name qaDbServer_temp -d mcr.microsoft.com/mssql/server:2022-latest || (echo Failed to run container)

create_missing_folders: run_container
	@echo Creating missing folders
	@(docker exec -u 0 qaDbServer_temp mkdir /var/opt/mssql/backup || (echo Failed to create backup folder))
	@(docker exec -u 0 qaDbServer_temp mkdir /usr/src/app/ || (echo Failed to create app folder; exit 1))

add_restore: create_missing_folders
	@echo Adding restore files
	@(docker cp vspqa.bak qaDbServer_temp:/var/opt/mssql/backup/vspqa.bak || (echo Failed to copy backup file))
	@(docker cp restore.sql qaDbServer_temp:/usr/src/app/restore.sql || (echo Failed to copy restore script))

restore_db: add_restore
	@echo Restoring database
	@(docker exec -u 0 qaDbServer_temp /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P D4t4b4s3* -d master -i /usr/src/app/restore.sql -C || (echo Failed to restore database))
	
remove_restore_files: restore_db
	@echo Removing restore files from container
	@(docker exec -u 0 qaDbServer_temp rm /var/opt/mssql/backup/vspqa.bak || (echo Failed to remove backup file))
	@(docker exec -u 0 qaDbServer_temp rm /usr/src/app/restore.sql || (echo Failed to remove restore script))

create_image: remove_restore_files
	@echo Creating image
	@docker commit qaDbServer_temp qa_db_server:latest || (echo Failed to create image)

everything: create_image
	@echo Stopping container
	@docker stop qaDbServer_temp || (echo Failed to stop container)
