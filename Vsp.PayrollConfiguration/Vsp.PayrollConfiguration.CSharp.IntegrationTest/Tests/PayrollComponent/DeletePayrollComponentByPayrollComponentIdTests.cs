using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class DeletePayrollComponentByPayrollComponentIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponent";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_PayrollComponent_DELETE_PA_2024 = Guid.Parse("00000993-07e8-0000-0000-000000000000");

    private static readonly Guid QA_DeletableComponent_PA = Guid.Parse("00000993-07e8-01db-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: (475)
    private static readonly Guid QA_DependentChildRuleOnly_WM = Guid.Parse("00000992-07e8-006e-0000-000000000000");// Level: QA_PayrollComponent_DELETE_WM, Year: 2024, Component: (110)
    private static readonly Guid QA_DependentChildRuleOnly_CLA = Guid.Parse("00000991-07e8-0070-0000-000000000000");// Level: QA_PayrollComponent_DELETE_CLA, Year: 2024, Component: (112)

    private static readonly Guid QA_ComponentFor_52001 = Guid.Parse("00000993-07e8-0069-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52001 (102)
    private static readonly Guid QA_ComponentFor_52002 = Guid.Parse("00000993-07e8-0067-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52002 (103)
    private static readonly Guid QA_ComponentFor_52003 = Guid.Parse("00000993-07e8-01a4-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52003 (420)
    private static readonly Guid QA_ComponentFor_52004 = Guid.Parse("00000993-07e9-01a8-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2025, Component: Check_52004 (424)
    private static readonly Guid QA_ComponentFor_52005 = Guid.Parse("00000993-07e8-0152-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52005 (338)
    private static readonly Guid QA_ComponentFor_52006 = Guid.Parse("00000993-07e8-0177-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52006 (375)
    private static readonly Guid QA_ComponentFor_52007 = Guid.Parse("00000993-07e8-0032-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52007 (50)
    private static readonly Guid QA_ComponentFor_52008 = Guid.Parse("00000993-07e8-021f-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52008 (543)
    private static readonly Guid QA_ComponentFor_52009 = Guid.Parse("00000993-07e8-01aa-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52009 (426)
    private static readonly Guid QA_ComponentFor_52010 = Guid.Parse("00000993-07e8-0176-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52010 (374)
    private static readonly Guid QA_ComponentFor_52011 = Guid.Parse("00000993-07e8-0068-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52011 (104)
    private static readonly Guid QA_ComponentFor_52012 = Guid.Parse("00000993-07e8-0033-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52012 (51)
    private static readonly Guid QA_ComponentFor_52013 = Guid.Parse("00000993-07e8-0034-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52013 (52)
    private static readonly Guid QA_ComponentFor_52015 = Guid.Parse("00000995-07e4-03b9-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2020, Component: Check_52015 (953)
    private static readonly Guid QA_ComponentFor_52016 = Guid.Parse("00000993-07e8-0232-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52016 (562)
    private static readonly Guid QA_ComponentFor_52017 = Guid.Parse("00000993-07e8-036b-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52017 (875)
    private static readonly Guid QA_ComponentFor_52018 = Guid.Parse("00000993-07e8-0054-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52018 (84)
    private static readonly Guid QA_ComponentFor_52019 = Guid.Parse("00000993-07e8-0086-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52019 (134)
    private static readonly Guid QA_ComponentFor_52020 = Guid.Parse("00000993-07e8-0c09-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52020 (3081)
    private static readonly Guid QA_ComponentFor_52020_Ok = Guid.Parse("00000993-07e8-0c0a-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52020_Ok (3082)
    private static readonly Guid QA_ComponentFor_52021 = Guid.Parse("00000993-07e8-0097-0000-000000000000");// Level: QA_PayrollComponent_DELETE_PA, Year: 2024, Component: Check_52021 (151)

    [Fact]
    public async Task Ok_QA_PayrollComponent_DELETE_PA()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_DeletableComponent_PA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsByYearIdAsync}".Replace("{yearId:guid}", QA_PayrollComponent_DELETE_PA_2024.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<PayrollComponentModel>>(getResult!)!;
        getObject!.Collection!.Any(pcm => pcm.Id == QA_DeletableComponent_PA).Should().BeFalse();
    }

    [Fact]
    public async Task BadRequest_MessageCode_DependentChildRuleOnly_WM()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_DependentChildRuleOnly_WM.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_DependentChildRuleOnly_CLA()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_DependentChildRuleOnly_CLA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52001()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52001.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52002()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52002.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52003()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52003.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52004()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52004.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52005()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52005.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52006()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52006.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52007()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52007.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52008()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52008.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52009()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52009.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52010()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52010.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52011()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52011.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52012()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52012.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52013()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52013.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    // Test for rule 52014 is intentionally excluded as it refers to obsolete functionality (holiday fund)

    [Fact]
    public async Task BadRequest_MessageCode_52015()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52015.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52016()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52016.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52017()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52017.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52018()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52018.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52019()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52019.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_52020()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52020.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult); // Here we accept to extra failing rules (52018 and 52021) because creating data for an isolated case if very difficult.
    }

    [Fact]
    public async Task Ok_MessageCode_52020()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52020_Ok.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult); // This component does NOT override the BalanceSheetSide, so it CAN be deleted.
    }

    [Fact]
    public async Task BadRequest_MessageCode_52021()
    {
        // Act
        var deleteUri = $"{PayrollComponentRoutes.DeletePayrollComponentByPayrollComponentIdAsync}"
            .Replace("{payrollComponentId:guid}", QA_ComponentFor_52021.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }
}