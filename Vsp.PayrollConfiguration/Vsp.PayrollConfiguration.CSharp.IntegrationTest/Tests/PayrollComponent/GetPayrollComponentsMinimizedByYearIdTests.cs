using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollComponent.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponent;

[Collection(EntityNames.PayrollComponent)]
public class GetPayrollComponentsMinimizedByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponent";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_PayrollComponent_GET_CLA_2025 = Guid.Parse("0000095e-07e9-0000-0000-000000000000");
    private static readonly Guid QA_PayrollComponent_GET_WM_2025 = Guid.Parse("00000960-07e9-0000-0000-000000000000");
    private static readonly Guid QA_PayrollComponent_GET_PA_2025 = Guid.Parse("00000961-07e9-0000-0000-000000000000");

    private const string OrderBy = "orderBy=key";
    private const string OrderByDesc = "orderBy=-key";
    private const string FilterByCategoryKey = "filter=category.key eq 21";
    private const string FilterByDescription = "filter=description lk 'NEVENINKOMS_PA'";

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_CLA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_CLA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_WM_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_WM_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_PayrollComponent_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_OrderBy_Key_Desc_QA_PayrollComponent_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderByDesc}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_Category_Key()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            $"&{FilterByCategoryKey}"; // ABP category
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_Description()
    {
        // Act
        var getUri =
            $"{PayrollComponentRoutes.GetPayrollComponentsMinimizedByYearIdAsync}"
                .Replace("{yearId:guid}", QA_PayrollComponent_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            $"&{FilterByDescription}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}
