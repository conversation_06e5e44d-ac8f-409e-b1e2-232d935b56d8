using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMinimum.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMinimum;

[Collection(EntityNames.BaseForCalculationAgeBasedMinimum)]
public class GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMinimum";
    protected override bool UseTransaction => false;

    private const string FilterByAge = "filter=age eq '50'";
    private const string FilterById = "filter=id eq '000009dc-07e9-0001-2300-030000000000'";
    private const string FilterByMinimum = "filter=minimum eq '40,15'";
    private const string OrderBy = "orderBy=age";
    private const string OrderByDesc = "orderBy=-age";
    private static readonly Guid QA_BFC_AgeBasedMinimum_GET_CLA_BFC1 = Guid.Parse("000009dc-07e9-0001-0100-000000000000");
    private static readonly Guid QA_BFC_AgeBasedMinimum_GET_WM_BFC1 = Guid.Parse("000009dd-07e9-0001-0100-000000000000");
    private static readonly Guid QA_BFC_AgeBasedMinimum_GET_PA_BFC1 = Guid.Parse("000009de-07e9-0001-0100-000000000000");

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_GET_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_CLA_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_GET_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_WM_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_WM_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_PA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_PA_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_OrderBy_Age_Desc() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_CLA_BFC1 year 2025
                      $"?{OrderByDesc}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Age_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_WM_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_WM_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterByAge}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Minimum_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_PA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_PA_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterByMinimum}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Id_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMinimumRoutes.GetBaseForCalculationAgeBasedMinimumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMinimum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMinimum_CLA_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterById}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );
}