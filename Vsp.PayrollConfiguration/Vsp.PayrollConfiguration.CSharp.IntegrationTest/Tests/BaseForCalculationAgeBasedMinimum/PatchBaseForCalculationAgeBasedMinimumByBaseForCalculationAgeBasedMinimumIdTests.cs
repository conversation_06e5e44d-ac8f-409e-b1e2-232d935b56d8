using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMinimum.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMinimum;

[Collection(EntityNames.BaseForCalculationAgeBasedMinimum)]
public class PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMinimum";
    protected override bool UseTransaction => true;

    private const string QA_BFC_AgeBasedMinimum_PATCH_CLA = "000009ee-07e9-0001-3200-050000000000"; // BaseForCalculation_AgeBasedMinimum for CLA - Year 2025 - Age 50
    private const string QA_BFC_AgeBasedMinimum_PATCH_WM = "000009ef-07e9-0001-1e00-020000000000"; // BaseForCalculation_AgeBasedMinimum for WM - Year 2025 - Age 30
    private const string QA_BFC_AgeBasedMinimum_PATCH_PA = "000009f0-07e9-0001-4100-0c0000000000"; // BaseForCalculation_AgeBasedMinimum for PA - Year 2025 - Age 65

    #region OK Tests

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_PATCH_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMinimumPatchModel { Minimum = 0m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_PATCH_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_WM),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMinimumPatchModel { Minimum = 999999.99m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMinimum_PATCH_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_PA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMinimumPatchModel { Minimum = 0.152365m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    #endregion

    #region BadRequest - ModelStateValidation

    [Fact]
    public async Task BadRequest_ModelValidation_NoPatchModel()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = null
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MinimumIsMissing()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new object()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MinimumIsNull()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMinimumPatchModel()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-1)]
    [InlineData(1000000)]
    public async Task BadRequest_ModelValidation_Minimum_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMinimumRoutes.PatchBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumIdAsync
                    .Replace("{baseForCalculationAgeBasedMinimumId:guid}", QA_BFC_AgeBasedMinimum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMinimumPatchModel { Minimum = value }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    #endregion
}