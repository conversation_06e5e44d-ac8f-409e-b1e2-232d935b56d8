using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Year.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Year;

[Collection(EntityNames.Year)]
public class GetYearsByWageModelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Year";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_Year_GET_WM = Guid.Parse("458d0541-8bdb-4d35-8c6e-606297d15db8");

    private const string OrderBy = "orderBy=year";
    private const string OrderByDesc = "orderBy=-year";

    private const string FilterYearId = "0000093c-07e8-0000-0000-000000000000"; // 2024
    private const string FilterUnexistentYearId = "0000093c-07e3-0000-0000-000000000000"; // 2019


    [Fact]
    public async Task Ok_OrderBy_Year()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByWageModelIdAsync}?{OrderBy}".Replace("{wageModelId:guid}", QA_Year_GET_WM.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_OrderBy_Year_Desc()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByWageModelIdAsync}?{OrderByDesc}".Replace("{wageModelId:guid}", QA_Year_GET_WM.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_FilterBy_Id()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByWageModelIdAsync}?{OrderBy}&filter=id eq '{FilterYearId}'".Replace("{wageModelId:guid}", QA_Year_GET_WM.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_Empty()
    {
        // Act
        var getUri = $"{YearRoutes.GetYearsByWageModelIdAsync}?{OrderBy}&filter=id eq '{FilterUnexistentYearId}'".Replace("{wageModelId:guid}", QA_Year_GET_WM.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}
