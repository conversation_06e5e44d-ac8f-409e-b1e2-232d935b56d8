using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollAdministration.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollAdministration;

[Collection(EntityNames.PayrollAdministration)]
public class GetPayrollAdministrationByPayrollAdministrationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollAdministration";
    protected override bool UseTransaction => false;

    private const string PayrollAdministrationIdFor_QA_PayrollConfiguration1 = "bbe616a2-4e59-4d39-8165-ee37a9708be9";
    private const string PayrollAdministrationIdFor_QA_PayrollConfiguration2 = "8d20dffc-49b6-454c-93e1-574d6a4dcb06";

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_PA_ForLevel_PA()
    {
        // Act
        var getUri =
            PayrollAdministrationRoutes.GetPayrollAdministrationByPayrollAdministrationIdAsync.Replace(
                "{payrollAdministrationId:guid}",
                PayrollAdministrationIdFor_QA_PayrollConfiguration1);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    [Claims(ClientName = "Loket3", Omgeving = 11, UserId = "5ad6ed4e-e059-4c21-a77f-04955458c181", Rol = RolEnum.Provider)] // QA_PayrollConfiguration2
    public async Task Ok_QA_PayrollConfiguration2_PA_ForLevel_PA()
    {
        // Act
        var getUri =
            PayrollAdministrationRoutes.GetPayrollAdministrationByPayrollAdministrationIdAsync.Replace(
                "{payrollAdministrationId:guid}",
                PayrollAdministrationIdFor_QA_PayrollConfiguration2);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}