using Vsp.PayrollConfiguration.BaseForCalculationBasePayrollComponent.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationBasePayrollComponent;

[Collection(EntityNames.BaseForCalculationBasePayrollComponent)]
public class GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberTests(
    WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationBasePayrollComponent";
    protected override bool UseTransaction => false;
    private const string baseForCalculationIdCLA = "000009d1-07e9-0001-0100-000000000000";

    [Fact]
    public async Task Ok_BFC_BasePayrollComponent_GET_CLA_AvailableComponentsForPeriod1() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdCLA).Replace("{payrollPeriodNumber}", "1"),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_BFC_BasePayrollComponent_GET_CLA_AvailableComponentsForPeriodGreaterThan1() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdCLA).Replace("{payrollPeriodNumber}", "2"),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Theory]
    [InlineData("0")]
    [InlineData("13")]
    public async Task BadRequest_MessageCode_BFC_BasePayrollComponent_GET_CLA_InvalidPayrollPeriodNumber(string payrollPeriodNumber) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationBasePayrollComponentRoutes.GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync}".Replace(
                    "{baseForCalculationId:guid}", baseForCalculationIdCLA).Replace("{payrollPeriodNumber}", payrollPeriodNumber),
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            }
        );
}