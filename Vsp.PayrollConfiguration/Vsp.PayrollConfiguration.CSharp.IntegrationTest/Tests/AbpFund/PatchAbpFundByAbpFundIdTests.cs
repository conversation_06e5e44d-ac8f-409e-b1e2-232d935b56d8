using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.AbpFund.Mappers;
using Vsp.PayrollConfiguration.Domain.Shared.Mappers;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.AbpFund;

[Collection(EntityNames.AbpFund)]
public class PatchAbpFundByAbpFundIdTests(
    WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture, [new AbpFundProfile(), new SharedProfile()])
{
    protected override string FolderName => "AbpFund";
    protected override bool UseTransaction => true;
}