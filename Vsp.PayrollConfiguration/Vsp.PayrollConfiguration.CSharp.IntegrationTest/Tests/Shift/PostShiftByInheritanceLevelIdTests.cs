using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Shift.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Shift;

[Collection(EntityNames.Shift)]
public class PostShiftByInheritanceLevelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Shift";
    protected override bool UseTransaction => true;

    // Only year 2025
    public static readonly Guid QA_Shift_POST_CLA = Guid.Parse("2dd04c2c-d805-4d74-90eb-0a5fe9d80305");
    public static readonly Guid QA_Shift_POST_WM = Guid.Parse("94e19aba-cad5-401f-b5a3-a3d3e7a5ed1a");
    public static readonly Guid QA_Shift_POST_PA = Guid.Parse("dc9b66ed-9a97-43de-82ab-54945a4babd9");

    // Years 2020 to 2025
    public static readonly Guid QA_Shift_POST_CLA_2020 = Guid.Parse("75cfeee3-c283-495e-873b-40e0245db1df");
    public static readonly Guid QA_Shift_POST_WM_2020 = Guid.Parse("99d571b5-1cf1-43ca-ba30-955c17c5151a");
    public static readonly Guid QA_Shift_POST_PA_2020 = Guid.Parse("85a63076-e328-4963-a3d9-c3fe43892dc1");

    [Fact]
    public async Task Ok_QA_Shift_POST_CLA() // Also validating a succeeding first period insertion
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?collectiveLaborAgreementId={QA_Shift_POST_CLA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_Shift_POST_WM()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?wageModelId={QA_Shift_POST_WM}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 2,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.20m,
            BonusPercentage = 14.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_Shift_POST_PA()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.28m,
            BonusPercentage = 9.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_Shift_POST_PA_WithFirstPeriodInCurrentLevel()
    {
        // Act
        HttpApiResponse postResult = null!;
        for (var periodNumber = 1; periodNumber < 3; periodNumber++)
        {
            var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                          $"?payrollAdministrationId={QA_Shift_POST_PA}";
            var postModel = new ShiftPostModel
            {
                ShiftNumber = 1,
                Year = 2025,
                StartPayrollPeriod = new() { PeriodNumber = periodNumber },
                FullTimeHoursPerWeek = periodNumber + 0.28m,
                BonusPercentage = 9.5M,
            };
            postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        }

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_Shift_POST_PA_WithFirstPeriodInParentLevel()
    {
        // Act
        HttpApiResponse postResult = null!;
        for (var periodNumber = 1; periodNumber < 3; periodNumber++)
        {
            var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                          $"?{(periodNumber == 1
                                  ? "wageModelId=" + QA_Shift_POST_WM
                                  : "payrollAdministrationId=" + QA_Shift_POST_PA)}";
            var postModel = new ShiftPostModel
            {
                ShiftNumber = 1,
                Year = 2025,
                StartPayrollPeriod = new() { PeriodNumber = periodNumber },
                FullTimeHoursPerWeek = periodNumber + 0.28m,
                BonusPercentage = 9.5M,
            };
            postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);
        }

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_QA_Shift_POST_PA_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?payrollAdministrationId={QA_Shift_POST_PA}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2025,
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 1.28m,
                    BonusPercentage = 9.5M,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shift was not created
        using var loketContext = GetLoketContext();
        var shift = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2389 // QA_Shift_POST_PA
                && s.YearId == 2025
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .SingleOrDefaultAsync();
        shift.Should().BeNull();
    }

    #region AddFutureYears

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddSingle()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddSingle_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2024,
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 1.23m,
                    BonusPercentage = 10.5M,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shifts were not created
        using var loketContext = GetLoketContext();
        var shifts = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2440 // QA_Shift_POST_CLA_2020
                && s.YearId >= 2024
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .ToListAsync();
        shifts.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddMultiple()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2020,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_CLA_AddMultiple_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2020,
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 1.23m,
                    BonusPercentage = 10.5M,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shifts were not created
        using var loketContext = GetLoketContext();
        var shifts = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2440 // QA_Shift_POST_CLA_2020
                && s.YearId >= 2020
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .ToListAsync();
        shifts.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_AddFutureYears_WM_AddSingle()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?wageModelId={QA_Shift_POST_WM_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_WM_AddMultiple()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?wageModelId={QA_Shift_POST_WM_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2020,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_PA_AddSingle()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_PA_AddMultiple()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2020,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_FirstPeriod_CurrentLevel()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 2, // Exists on PA level for year 2023 (period 1), and year 2024 (period 1)
            Year = 2021,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_FirstPeriod_ParentLevel()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 3, // Exists on WM level for year 2023 (period 1), and year 2024 (period 1), and year 2025 (period 1)
            Year = 2021,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_LaterPeriod_CurrentLevel()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 4, // Exists on PA level for year 2023 (period 2), and year 2024 (period 1)
            Year = 2021,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_StopWhenEntityExists_LaterPeriod_ParentLevel()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 5, // Exists on WM level for year 2023 (period 2), and year 2024 (period 1), and year 2025 (period 1)
            Year = 2021,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.23m,
            BonusPercentage = 10.5M,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task Ok_AddFutureYears_AddFutureYearsWithFirstPayrollPeriod()
    {
        // Arrange
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA_2020}";

        var postModel = new ShiftPostModel
        {
            ShiftNumber = 6,
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 2 }, // Shift 6 already exists for year 2024 and period 1 - but NOT for year 2025
            FullTimeHoursPerWeek = 6,
            BonusPercentage = 6,
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);

        var loketContext = GetLoketContext();

        var shift2024 = new Repository.Entities.Shift { InheritanceLevelId = 2442, YearId = 2024, ShiftId = 6, PayrollPeriodId = 2 };
        shift2024 = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Shift>(shift2024.Id))
            .SingleOrDefaultAsync();
        shift2024.Should().NotBeNull();

        var shift2025 = new Repository.Entities.Shift { InheritanceLevelId = 2442, YearId = 2025, ShiftId = 6, PayrollPeriodId = 1 };
        shift2025 = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Shift>(shift2025.Id))
            .SingleOrDefaultAsync();
        shift2025.Should().NotBeNull();
    }

    [Fact]
    public async Task BadRequest_AddFutureYears_CLA_AddSingle()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}",
                Method = HttpMethod.Post,
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2019, // Year doesn't exist
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 1.23m,
                    BonusPercentage = 10.5M,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shifts were not created
        using var loketContext = GetLoketContext();
        var shifts = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2440 // QA_Shift_POST_CLA_2020
                && s.YearId >= 2019
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .ToListAsync();
        shifts.Should().HaveCount(0);
    }

    [Fact]
    public async Task BadRequest_AddFutureYears_CLA_AddSingle_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_Shift_POST_CLA_2020}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2019, // Year doesn't exist
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 1.23m,
                    BonusPercentage = 10.5M,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shifts were not created
        using var loketContext = GetLoketContext();
        var shifts = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2440 // QA_Shift_POST_CLA_2020
                && s.YearId >= 2019
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .ToListAsync();
        shifts.Should().HaveCount(0);
    }

    #endregion

    #region ModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_EmptyPayload()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel();
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(16)]
    public async Task BadRequest_ModelValidation_ShiftNumber_OutOfRange(int shiftNumber)
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = shiftNumber,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.234m,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_StartPayrollPeriodProperties_Null()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = null,
            StartPayrollPeriod = new() { PeriodNumber = null },
            FullTimeHoursPerWeek = 1.234m,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Theory]
    [InlineData(1899)]
    [InlineData(10000)]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_Year_OutOfRange(int year)
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = year,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 1.234m,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(54)]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_PeriodNumber_OutOfRange(int periodNumber)
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = periodNumber },
            FullTimeHoursPerWeek = 1.234m,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    [InlineData(100)]
    public async Task BadRequest_ModelValidation_FullTimeHoursPerWeek_OutOfRange(decimal fullTimeHoursPerWeek)
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = fullTimeHoursPerWeek,
            BonusPercentage = 10.5M,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_FullTimeHoursPerWeek_MoreThanTwoDecimals()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = @"
        {
            ""shiftNumber"": 1,
            ""year"": 2025,
            ""startPayrollPeriod"": {
             ""periodNumber"": 1
             },
             ""fullTimeHoursPerWeek"": 1.234,
             ""bonusPercentage"": 10.5
        }";
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Theory]
    [InlineData(-0.001)]
    [InlineData(100.001)]
    public async Task BadRequest_ModelValidation_BonusPercentage_OutOfRange(decimal bonusPercentage)
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 3.67M,
            BonusPercentage = bonusPercentage,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_BonusPercentage_MoreThanThreeDecimals()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = @"
        {
            ""shiftNumber"": 1,
            ""year"": 2025,
            ""startPayrollPeriod"": {
             ""periodNumber"": 1
             },
             ""fullTimeHoursPerWeek"": 1.23,
             ""bonusPercentage"": 10.5765
        }";
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    #endregion

    #region MessageCode

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            FullTimeHoursPerWeek = 3.67M,
            BonusPercentage = 3,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}?payrollAdministrationId={QA_Shift_POST_PA}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = new ShiftPostModel
                {
                    ShiftNumber = 1,
                    Year = 2024,
                    StartPayrollPeriod = new() { PeriodNumber = 1 },
                    FullTimeHoursPerWeek = 3.67M,
                    BonusPercentage = 3,
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shift was not created
        using var loketContext = GetLoketContext();
        var shift = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(s => s.InheritanceLevelId == 2389 // QA_Shift_POST_PA
                && s.YearId == 2024
                && s.ShiftId == 1
                && s.PayrollPeriodId == 1)
            .SingleOrDefaultAsync();
        shift.Should().BeNull();
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_DoesNotExist()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 13 }, // Monthly payrolling, so max is 12
            FullTimeHoursPerWeek = 3.67M,
            BonusPercentage = 3,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_NoDataForFirstPayrollPeriod()
    {
        // Act
        var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                      $"?payrollAdministrationId={QA_Shift_POST_PA}";
        var postModel = new ShiftPostModel
        {
            ShiftNumber = 1,
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 2 },
            FullTimeHoursPerWeek = 3.67M,
            BonusPercentage = 3,
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_EntityAlreadyExistsInCurrentLevel()
    {
        // Act
        HttpApiResponse postResult = null!;
        for (var entity = 1; entity < 3; entity++)
        {
            var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                          $"?payrollAdministrationId={QA_Shift_POST_PA}";
            var postModel = new ShiftPostModel
            {
                ShiftNumber = 1,
                Year = 2025,
                StartPayrollPeriod = new() { PeriodNumber = 1 },
                FullTimeHoursPerWeek = 1.28m,
                BonusPercentage = 9.5M,
            };
            postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, entity == 1 ? HttpStatusCode.Created : HttpStatusCode.BadRequest);
        }

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task BadRequest_MessageCode_EntityAlreadyExistsInParentLevel()
    {
        // Act
        HttpApiResponse postResult = null!;
        for (var entity = 1; entity < 3; entity++)
        {
            var postUri = $"{ShiftRoutes.PostShiftByInheritanceLevelIdAsync}" +
                          $"?{(entity == 1
                                  ? "wageModelId=" + QA_Shift_POST_WM
                                  : "payrollAdministrationId=" + QA_Shift_POST_PA)}";
            var postModel = new ShiftPostModel
            {
                ShiftNumber = 1,
                Year = 2025,
                StartPayrollPeriod = new() { PeriodNumber = 1 },
                FullTimeHoursPerWeek = 1.28m,
                BonusPercentage = 9.5M,
            };
            postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, entity == 1 ? HttpStatusCode.Created : HttpStatusCode.BadRequest);
        }

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    #endregion
}