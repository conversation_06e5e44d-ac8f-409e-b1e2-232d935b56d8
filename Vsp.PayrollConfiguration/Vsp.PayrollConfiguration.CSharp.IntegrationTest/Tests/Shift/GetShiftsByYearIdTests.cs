using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Shift.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Shift;

[Collection(EntityNames.Shift)]
public class GetShiftsByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Shift";
    protected override bool UseTransaction => false;

    private static class Ids
    {
        public static readonly Guid QA_Shift_GET_CLA_2025 = Guid.Parse("0000094c-07e9-0000-0000-000000000000");
        public static readonly Guid QA_Shift_GET_WM_2025 = Guid.Parse("0000094d-07e9-0000-0000-000000000000");
        public static readonly Guid QA_Shift_GET_PA_2025 = Guid.Parse("0000094e-07e9-0000-0000-000000000000");
    }

    private const string OrderByDesc = "orderBy=-shiftNumber,-startPayrollPeriod.payrollPeriodId";

    [Fact]
    public async Task Ok_QA_Shift_GET_CLA_2025()
    {
        // Act
        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Ids.QA_Shift_GET_CLA_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_GET_WM_2025()
    {
        // Act
        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Ids.QA_Shift_GET_WM_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_GET_PA_2025()
    {
        // Act
        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Ids.QA_Shift_GET_PA_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_OrderBy_QA_Shift_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Ids.QA_Shift_GET_PA_2025.ToString()) +
            $"?{OrderByDesc}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_QA_Shift_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Ids.QA_Shift_GET_PA_2025.ToString()) +
            $"?{OrderByDesc}" +
            "&filter=startPayrollPeriod.year eq 2025 and startPayrollPeriod.periodNumber gt 1 and startPayrollPeriod.periodNumber lt 3";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}