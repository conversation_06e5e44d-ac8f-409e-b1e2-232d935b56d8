using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.Shift.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Shift.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Shift;

[Collection(EntityNames.Shift)]
public class DeleteShiftByShiftIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Shift";
    protected override bool UseTransaction => true;

    // In QA_Shift_DELETE_CLA_ForLevel_CLA
    public static readonly Guid Shift_CLA = Guid.Parse("00000959-07e9-0001-0100-000000000000");
    public static readonly Guid Shift_CLA_2025 = Guid.Parse("00000959-07e9-0000-0000-000000000000");

    // In QA_Shift_DELETE_WM_ForLevel_WM
    public static readonly Guid Shift_WM = Guid.Parse("0000095b-07e9-0001-0100-000000000000");
    public static readonly Guid Shift_WM_2025 = Guid.Parse("0000095b-07e9-0000-0000-000000000000");

    // In QA_Shift_DELETE_WM_ForLevel_PA
    public static readonly Guid Shift_PA = Guid.Parse("00000958-07e9-0002-0200-000000000000");
    public static readonly Guid Shift_PA_2025 = Guid.Parse("00000958-07e9-0000-0000-000000000000");

    // For BadRequests
    public static readonly Guid ShiftHasChildren = Guid.Parse("0000095a-07e9-0001-0100-000000000000");
    public static readonly Guid ShiftFirstPeriod = Guid.Parse("00000958-07e9-0002-0100-000000000000");

    [Fact]
    public async Task Ok_QA_Shift_DELETE_CLA_ForLevel_CLA()
    {
        // Act
        var deleteUri = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
            .Replace("{shiftId:guid}", Shift_CLA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Shift_CLA_2025.ToString()) +
                     $"?filter=id eq '{Shift_CLA}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<ShiftModel>>(getResult!)!;
        getObject.Collection!.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_Shift_DELETE_WM_ForLevel_WM() // Also tests that values defined at parent remain
    {
        // Act
        var deleteUri = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
            .Replace("{shiftId:guid}", Shift_WM.ToString());
        await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Shift_WM_2025.ToString()) +
                     $"?filter=id eq '{Shift_WM}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_Shift_DELETE_PA_ForLevel_PA()
    {
        // Act
        var deleteUri = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
            .Replace("{shiftId:guid}", Shift_PA.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(deleteResult);

        var getUri = $"{ShiftRoutes.GetShiftsByYearIdAsync}".Replace("{yearId:guid}", Shift_PA_2025.ToString()) +
                     $"?filter=id eq '{Shift_PA}'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);
        var getObject = JsonConvert.DeserializeObject<ListResult<ShiftModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    [Fact]
    public async Task Ok_QA_Shift_DELETE_PA_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
                    .Replace("{shiftId:guid}", Shift_PA.ToString()),
                Method = HttpMethod.Delete,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shift was not deleted
        using var loketContext = GetLoketContext();
        var shift = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Shift>(Shift_PA))
            .SingleOrDefaultAsync();
        shift.Should().NotBeNull();
    }

    #region Inheritance Entity DELETE validator

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Delete_EntityHasChildren"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_EntityHasChildren()
    {
        // Act
        var deleteUri = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
            .Replace("{shiftId:guid}", ShiftHasChildren.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    [Fact]
    public async Task BadRequest_MessageCode_EntityHasChildren_ValidateOnly()
    {
        await VerifyCallAsync(
            new Request
            {
                Url = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
                    .Replace("{shiftId:guid}", ShiftHasChildren.ToString()),
                Method = HttpMethod.Delete,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check database to make sure shift was not deleted
        using var loketContext = GetLoketContext();
        var shift = await loketContext.Set<Repository.Entities.Shift>().AsNoTracking()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Shift>(ShiftHasChildren))
            .SingleOrDefaultAsync();
        shift.Should().NotBeNull();
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodCannotBeDeleted()
    {
        // Act
        var deleteUri = $"{ShiftRoutes.DeleteShiftByShiftIdAsync}"
            .Replace("{shiftId:guid}", ShiftFirstPeriod.ToString());
        var deleteResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(deleteResult);
    }

    #endregion
}