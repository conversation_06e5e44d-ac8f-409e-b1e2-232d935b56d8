using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.UnitPercentage;

[Collection(EntityNames.UnitPercentage)]
public class GetUnitPercentageMetadataByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "UnitPercentage";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_UnitPercentage_GET_CLA_2025 = Guid.Parse("000008fb-07e9-0000-0000-000000000000");
    private static readonly Guid QA_UnitPercentage_GET_WM_2025 = Guid.Parse("000008fc-07e9-0000-0000-000000000000");
    private static readonly Guid QA_UnitPercentage_GET_PA_2025 = Guid.Parse("000008fd-07e9-0000-0000-000000000000");


    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_CLA_2025()
    {
        // Act
        var getUri = $"{UnitPercentageRoutes.GetUnitPercentageMetadataByYearIdAsync}"
            .Replace("{yearId:guid}", QA_UnitPercentage_GET_CLA_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_WM_2025()
    {
        // Act
        var getUri = $"{UnitPercentageRoutes.GetUnitPercentageMetadataByYearIdAsync}"
            .Replace("{yearId:guid}", QA_UnitPercentage_GET_WM_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_PA_2025()
    {
        // Act
        var getUri = $"{UnitPercentageRoutes.GetUnitPercentageMetadataByYearIdAsync}"
            .Replace("{yearId:guid}", QA_UnitPercentage_GET_PA_2025.ToString());
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}