using Vsp.PayrollConfiguration.UnitPercentage.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.UnitPercentage;

public class GetUnitPercentagesByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "UnitPercentage";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_UnitPercentage_GET_CLA_2025 = Guid.Parse("000008fb-07e9-0000-0000-000000000000");
    private static readonly Guid QA_UnitPercentage_GET_WM_2025 = Guid.Parse("000008fc-07e9-0000-0000-000000000000");
    private static readonly Guid QA_UnitPercentage_GET_PA_2025 = Guid.Parse("000008fd-07e9-0000-0000-000000000000");

    private const string OrderBy = "orderBy=payrollComponent.key,startPayrollPeriod.periodNumber";

    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_CLA_2025()
    {
        // Act
        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", QA_UnitPercentage_GET_CLA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_WM_2025()
    {
        // Act
        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", QA_UnitPercentage_GET_WM_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_UnitPercentage_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", QA_UnitPercentage_GET_PA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_QA_UnitPercentage_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", QA_UnitPercentage_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            "&filter=id eq '000008fd-07e9-002b-0100-000000000000'";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_CalculateOver_Key()
    {
        // Act
        var getUri =
            $"{UnitPercentageRoutes.GetUnitPercentagesByYearIdAsync}".Replace("{yearId:guid}", QA_UnitPercentage_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            "&filter=calculateOver.key eq 0"; // non-nullable code table
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}