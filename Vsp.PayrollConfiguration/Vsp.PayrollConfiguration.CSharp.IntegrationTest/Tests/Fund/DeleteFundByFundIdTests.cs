using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.Fund;

[Collection(EntityNames.Fund)]
public class DeleteFundByFundIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "Fund";
    protected override bool UseTransaction => true;

    // TODO: Add test methods here
}