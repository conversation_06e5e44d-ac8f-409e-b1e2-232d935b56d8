using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMaximum.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMaximum;

[Collection(EntityNames.BaseForCalculationAgeBasedMaximum)]
public class PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMaximum";
    protected override bool UseTransaction => true;
    
    
    private const string QA_BFC_AgeBasedMaximum_PATCH_CLA = "000009f7-07e9-0001-0500-010000000000"; // BaseForCalculation_AgeBasedMaximum for CLA - Year 2025 - Age 5
    private const string QA_BFC_AgeBasedMaximum_PATCH_WM = "000009f8-07e9-0001-0500-010000000000"; // BaseForCalculation_AgeBasedMaximum for WM - Year 2025 - Age 5
    private const string QA_BFC_AgeBasedMaximum_PATCH_PA = "000009f9-07e9-0001-0500-010000000000"; // BaseForCalculation_AgeBasedMaximum for PA - Year 2025 - Age 5

    #region OK Tests

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_PATCH_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMaximumPatchModel { Maximum = 0m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_PATCH_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_WM),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMaximumPatchModel { Maximum = 999999.99m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_PATCH_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_PA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMaximumPatchModel { Maximum = 0.123456m }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

    #endregion

    #region BadRequest - ModelStateValidation

    [Fact]
    public async Task BadRequest_ModelValidation_NoPatchModel()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = null
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MaximumIsMissing()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new object()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Fact]
    public async Task BadRequest_ModelValidation_MaximumIsNull()
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMaximumPatchModel()
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    [Theory]
    [InlineData(-0.999999)]
    [InlineData(1000000)]
    public async Task BadRequest_ModelValidation_Maximum_OutOfRange(decimal value)
        => await VerifyCallAsync(
            new Request
            {
                Url = BaseForCalculationAgeBasedMaximumRoutes.PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync
                    .Replace("{baseForCalculationAgeBasedMaximumId:guid}", QA_BFC_AgeBasedMaximum_PATCH_CLA),
                Method = HttpMethod.Patch,
                Body = new BaseForCalculationAgeBasedMaximumPatchModel { Maximum = value }
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default
            }
        );

    #endregion
}