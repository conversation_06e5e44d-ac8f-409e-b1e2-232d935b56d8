using Vsp.PayrollConfiguration.BaseForCalculationAgeBasedMaximum.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculationAgeBasedMaximum;

[Collection(EntityNames.BaseForCalculationAgeBasedMaximum)]
public class GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculationAgeBasedMaximum";
    protected override bool UseTransaction => false;

    private const string FilterByAge = "filter=age eq '50'";
    private const string FilterById = "filter=id eq '000009f1-07e9-0001-2300-030000000000'";
    private const string FilterByMaximum = "filter=maximum eq '40,15'";
    private const string OrderBy = "orderBy=age";
    private const string OrderByDesc = "orderBy=-age";
    private static readonly Guid QA_BFC_AgeBasedMaximum_GET_CLA_BFC1 = Guid.Parse("000009f1-07e9-0001-0100-000000000000");
    private static readonly Guid QA_BFC_AgeBasedMaximum_GET_WM_BFC1 = Guid.Parse("000009f2-07e9-0001-0100-000000000000");
    private static readonly Guid QA_BFC_AgeBasedMaximum_GET_PA_BFC1 = Guid.Parse("000009f3-07e9-0001-0100-000000000000");

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_GET_CLA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_CLA_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_GET_WM_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_WM_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_WM_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_QA_BFC_AgeBasedMaximum_GET_PA_2025() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_PA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_PA_BFC1 year 2025
                      $"?{OrderBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_OrderBy_Age_Desc() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_CLA_BFC1 year 2025
                      $"?{OrderByDesc}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Age_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_WM_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_WM_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterByAge}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Maximum_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_PA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_PA_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterByMaximum}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Id_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationAgeBasedMaximumRoutes.GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync}"
                          .Replace("{baseForCalculationId:guid}", QA_BFC_AgeBasedMaximum_GET_CLA_BFC1.ToString()) + // QA_BaseForCalculation_AgeBasedMaximum_CLA_BFC1 year 2025
                      $"?{OrderBy}" +
                      $"&{FilterById}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );
}