using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.PayrollComponentExtra.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.PayrollComponentExtra;

[Collection(EntityNames.PayrollComponentExtra)]
public class GetPayrollComponentExtraMetadataByPayrollComponentIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture) : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "PayrollComponentExtra";
    protected override bool UseTransaction => false;

    //Level: QA_PayrollComponentExtra_GET_PA, Year: 2025, Component: EENHEID 11 (51)
    private static readonly Guid QA_ComponentIdWithUnitCategory = Guid.Parse("0000097a-07e9-0033-0000-000000000000");
    //Level: QA_PayrollComponentExtra_GET_PA, Year: 2025, Component: SUPPLETIE (100)
    private static readonly Guid QA_ComponentIdWithOtherCategory = Guid.Parse("0000097a-07e9-0064-0000-000000000000");

    [Fact]
    public async Task Ok_ReturnsPopulatedCollectionForComponentWithUnitCategory()
    {
        // Arrange
        var getUri = PayrollComponentExtraRoutes.GetPayrollComponentExtraMetadataByPayrollComponentIdAsync
            .Replace("{payrollComponentId:guid}", QA_ComponentIdWithUnitCategory.ToString());

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_ReturnsNullForComponentWithOtherCategory()
    {
        // Arrange
        var getUri = PayrollComponentExtraRoutes.GetPayrollComponentExtraMetadataByPayrollComponentIdAsync
            .Replace("{payrollComponentId:guid}", QA_ComponentIdWithOtherCategory.ToString());

        // Act
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}