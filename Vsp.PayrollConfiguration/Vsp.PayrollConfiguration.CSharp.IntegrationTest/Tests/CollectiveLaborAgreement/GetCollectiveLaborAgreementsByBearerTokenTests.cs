using Vsp.PayrollConfiguration.CollectiveLaborAgreement.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.CollectiveLaborAgreement;

[Collection(EntityNames.CollectiveLaborAgreement)]
public class GetCollectiveLaborAgreementsByBearerTokenTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "CollectiveLaborAgreement";
    protected override bool UseTransaction => false;

    private const string FilterByDescription = "filter=description lk 'QA_PayrollConfiguration'";
    private const string OrderByDescription = "orderBy=description";

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_OrderByDescription()
    {
        // Act
        var getUri = $"{CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    [Claims(ClientName = "Loket3", Omgeving = 11, UserId = "5ad6ed4e-e059-4c21-a77f-04955458c181", Rol = RolEnum.Provider)] // QA_PayrollConfiguration2
    public async Task Ok_QA_PayrollConfiguration2_OrderByDescription()
    {
        // Act
        var getUri = $"{CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_X_ReportInput_header() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{CollectiveLaborAgreementRoutes.GetCollectiveLaborAgreementsByBearerTokenAsync}?{FilterByDescription}&{OrderByDescription}",
                Method = HttpMethod.Get,
                Headers = new Dictionary<string, string>
                {
                    { "accept", "text/csv" },
                    { "content-type", "application/json" },
                    { "x-reportinput", "{\"FileNameWithoutExtension\":\"CollectiveLaborAgreements\",\"Fields\":[{\"FieldName\":\"id\",\"ReportColumnName\":\"Id\"},{\"FieldName\":\"description\",\"ReportColumnName\":\"Description\"},{\"FieldName\":\"comment\",\"ReportColumnName\":\"Comment\"}]}" }
                },
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                HeaderValidations = [
                    new HeaderValidation
                    {
                        Key = "Content-Type",
                        Value = "text/csv; charset=utf-8",
                        Mode = ValidationMode.Strict,
                    },
                    new HeaderValidation
                    {
                        Key = "Content-Disposition",
                        Value = @"attachment; filename=""CollectiveLaborAgreements .* ([0-1]\d|2[0-3]):[0-5]\d:[0-5]\d\.csv""",
                        Mode = ValidationMode.Pattern,
                    },
                ],
                BodyValidation = new BodyValidation(),
            }
        );
}