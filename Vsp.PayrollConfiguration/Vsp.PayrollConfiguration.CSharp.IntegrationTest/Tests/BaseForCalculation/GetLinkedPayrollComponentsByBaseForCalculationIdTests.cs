using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class GetLinkedPayrollComponentsByBaseForCalculationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => false;

    private const string FilterBy = "filter=id eq '000009a6-07e9-00d2-0000-000000000000'";
    private const string OrderBy = "orderBy=key";
    private const string OrderByDesc = "orderBy=-key";
    private static readonly Guid baseForCalculationIdCLA = Guid.Parse("000009a6-07e9-0001-0100-000000000000");

    [Theory]
    [InlineData("000009a6-07e9-0001-0100-000000000000")]
    [InlineData("000009a6-07e9-0001-0200-000000000000")]
    public async Task Ok_QA_LinkedComponentsBaseForCalculation_GET_CLA_2025(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId}?{OrderBy}".Replace("{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Theory]
    [InlineData("000009a7-07e9-0001-0100-000000000000")]
    [InlineData("000009a7-07e9-0002-0200-000000000000")]
    public async Task Ok_QA_LinkedComponentsBaseForCalculation_GET_WM_2025(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId}?{OrderBy}".Replace("{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Theory]
    [InlineData("000009a8-07e9-0003-0100-000000000000")]
    [InlineData("000009a8-07e9-0004-0100-000000000000")]
    public async Task Ok_QA_LinkedComponentsBaseForCalculation_GET_PA_2025(string baseForCalculationId) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId}?{OrderBy}".Replace("{baseForCalculationId:guid}", baseForCalculationId),
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_OrderBy_Key_Desc() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId}?{OrderByDesc}"
                    .Replace("{baseForCalculationId:guid}", baseForCalculationIdCLA.ToString()), // QA_BaseForCalculation_CLA year 2025
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );

    [Fact]
    public async Task Ok_Filter_Id_Equals() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.GetLinkedPayrollComponentsByBaseForCalculationId}"
                          .Replace("{baseForCalculationId:guid}", baseForCalculationIdCLA.ToString()) + // QA_BaseForCalculation_CLA year 2025
                      $"?{OrderBy}" +
                      $"&{FilterBy}",
                Method = HttpMethod.Get
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = new JsonBodyValidation()
            }
        );
}