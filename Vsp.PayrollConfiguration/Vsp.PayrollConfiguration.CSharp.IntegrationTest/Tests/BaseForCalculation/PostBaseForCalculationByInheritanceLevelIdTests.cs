using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Repository.Entities;
using Vsp.PayrollConfiguration.Repository.Enums;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class PostBaseForCalculationByInheritanceLevelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_BaseForCalculation_POST_CLA = Guid.Parse("5bfe800b-ac86-47fc-9a1a-79a9c18d9aca");
    private static readonly Guid QA_BaseForCalculation_POST_WM = Guid.Parse("e00e0220-01c2-4ad8-887b-817a8ec5b5b6");
    private static readonly Guid QA_BaseForCalculation_POST_PA = Guid.Parse("ca334c95-7c4e-46f7-ac54-8177f49304f5");

    private static readonly Guid QA_BaseForCalculation_POST_CLA_2018 = Guid.Parse("de79d924-9ff4-4e02-887b-20f2b4db6042");

    #region OK

    [Fact]
    public async Task Ok_QA_BaseForCalculation_POST_CLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 16;
                    model.Year = 2025;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculation_POST_WM() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?wageModelId={QA_BaseForCalculation_POST_WM}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model => { model.Year = 2025; model.Key = 5; }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task Ok_QA_BaseForCalculation_POST_PA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?payrollAdministrationId={QA_BaseForCalculation_POST_PA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model => { model.Year = 2025; model.Key = 10; }),
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

    #endregion

    #region ModelValidation

    [Fact]
    public async Task BadRequest_ModelValidation_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = ""
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

    [Fact]
    public async Task BadRequest_ModelValidation_Properties_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = new BaseForCalculationPostModel { Year = null, Key = null }
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_ModelValidation_SubProperties_Null() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = null })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Theory]
    [InlineData(1899, 2025, 1)] // Year too low
    [InlineData(10000, 2025, 1)] // Year too high
    [InlineData(2025, 0, 1)] // Key too low
    [InlineData(2025, 26, 1)] // Key too high
    [InlineData(2025, 1, 0)] // PeriodNumber too low
    [InlineData(2025, 1, 54)] // PeriodNumber too high
    public async Task BadRequest_ModelValidation_Boundaries(int year, int key, int periodNumber) =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m =>
                {
                    m.Year = year;
                    m.Key = key;
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = periodNumber };
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    #endregion

    #region MessageCodes

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m =>
                {
                    m.Year = 2024;
                    m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 13 };
                    m.CalculationPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    m.PayoutPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_DoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 13 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodDoesNotExist() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 2 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    // This test cannot be expression-bodied because it has multiple statements
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_ParentInheritanceLevel()
    {
        // Insert on parent (CLA), then try to insert on child (WM/PA) with same key/year/period
        var postModel = GetBaseForCalculationPostModel(m =>
        {
            m.Year = 2025;
            m.Key = 2;
            m.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
        });
        var postUriCLA = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}";
        var postUriWM = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?wageModelId={QA_BaseForCalculation_POST_WM}";

        // Insert on CLA
        await CallApiAsync(HttpMethod.Post, postUriCLA, postModel, HttpStatusCode.Created);

        // Try to insert on WM (should fail)
        await VerifyCallAsync(
            new Request { Url = postUriWM, Method = HttpMethod.Post, Body = postModel },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });
    }

    [Fact]
    public async Task BadRequest_MessageCode_Key_ReservedCLA() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?wageModelId={QA_BaseForCalculation_POST_WM}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.Key = 16)
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_MessageCode_ResultPayrollComponent_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.ResultPayrollComponent = new KeyModel { Key = 9999 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_MessageCode_ResultPayrollComponent_2() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.ResultPayrollComponent = new KeyModel { Key = 319 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_MessageCode_PayslipType_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.PayslipType = new KeyModel { Key = 9999 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    [Fact]
    public async Task BadRequest_MessageCode_AccrualEndPayrollPeriod_Invalid() =>
        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(m => m.AccrualEndPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 13 })
            },
            new ResponseValidation { ExpectedStatusCode = HttpStatusCode.BadRequest, BodyValidation = JsonBodyValidation.Default });

    #endregion

    #region AddArrangementPayrollComponents

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_SingleYear()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2025;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId == 2025
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(1);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(componentIds.Count);
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_SingleYear_ValidateOnly()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2025;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId == 2025
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(0);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(0);
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_SingleYear_ValidateOnly_WithWarning()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2025;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                    model.AccrualEndPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 2 }; // This will give a warning about extrapolation
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId == 2025
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(0);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2025
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(0);
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_MultipleYears()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2024;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (bfc.YearId == 2024 || bfc.YearId == 2025)
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(2);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(2 * componentIds.Count);
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_MultipleYears_MissingResultComponent()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2023 || c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2023;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 2 }; // Non-existing in year 2024 ONLY! (which should block BFC creation there, but NOT in 2023 and 2025)
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (bfc.YearId == 2023 || bfc.YearId == 2025)
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(2);

        // Check failed base for calculation count
        var failedBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId == 2024
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        failedBaseForCalculationCount.Should().Be(0); // BFC not valid so not added

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2023 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(2 * componentIds.Count);

        // Check failed component count
        var failedComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId == 2024
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        failedComponentCount.Should().Be(0); // BFC not valid so also no components added
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_MultipleYears_ExistingComponents()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 2)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(2); // In year 2024 component 211 already exists, in year 2025 component 846 already exists

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 2;
                    model.Year = 2024;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (bfc.YearId == 2024 || bfc.YearId == 2025)
                && bfc.BaseForCalculationId == 2
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(2);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(2 * componentIds.Count); // Already existing components are skipped, end result is all components present
    }

    [Fact]
    public async Task Ok_AddArrangementPayrollComponents_CLA_MultipleYears_ValidateOnly()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2024;
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.Created,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (bfc.YearId == 2024 || bfc.YearId == 2025)
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(0);

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && (c.YearId == 2024 || c.YearId == 2025)
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(0);
    }

    [Fact]
    public async Task BadRequest_AddArrangementPayrollComponents_CLA_SingleYear()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId >= 2017
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2017; // Year doesn't exist
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId >= 2017
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(0); // No base for calculations created

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId >= 2017
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(0); // No payroll components created
    }

    [Fact]
    public async Task BadRequest_AddArrangementPayrollComponents_CLA_SingleYear_ValidateOnly()
    {
        // Arrange
        using var loketContext = GetLoketContext();
        var componentIds = await loketContext.Set<ArrangementComponent>().AsNoTracking()
            .Where(ac => ac.ArrangementType == (int)ArrangementType.BaseForCalculation && ac.Identification == 1)
            .OrderBy(ac => ac.ComponentType)
            .Select(ac => ac.ComponentId)
            .ToListAsync();

        // Check old component count
        var oldComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId >= 2017
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        oldComponentCount.Should().Be(0);

        await VerifyCallAsync(
            new Request
            {
                Url = $"{BaseForCalculationRoutes.PostBaseForCalculationByInheritanceLevelIdAsync}?collectiveLaborAgreementId={QA_BaseForCalculation_POST_CLA_2018}",
                Method = HttpMethod.Post,
                Headers = new Dictionary<string, string>
                {
                    { "X-ValidateOnly", "true" }
                },
                Body = GetBaseForCalculationPostModel(model =>
                {
                    model.Key = 1;
                    model.Year = 2017; // Year doesn't exist
                    model.StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 };
                    model.ResultPayrollComponent = new KeyModel { Key = 1 };
                })
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.BadRequest,
                BodyValidation = JsonBodyValidation.Default,
            });

        // Check new base for calculation count
        var newBaseForCalculationCount = await loketContext.Set<Repository.Entities.BaseForCalculation>().AsNoTracking()
            .Where(bfc => bfc.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && bfc.YearId >= 2017
                && bfc.BaseForCalculationId == 1
                && bfc.PayrollPeriodId == 1)
            .CountAsync();
        newBaseForCalculationCount.Should().Be(0); // No base for calculations created

        // Check new component count
        var newComponentCount = await loketContext.Set<Component>().AsNoTracking()
            .Where(c => c.InheritanceLevelId == 2512 // QA_BaseForCalculation_POST_CLA_2018
                && c.YearId >= 2017
                && componentIds.Contains(c.ComponentId))
            .CountAsync();
        newComponentCount.Should().Be(0); // No payroll components created
    }

    #endregion

    #region Post model generation

    private BaseForCalculationPostModel GetBaseForCalculationPostModel(Action<BaseForCalculationPostModel>? setNewPropertyValues = null)
    {
        var postModel = new BaseForCalculationPostModel
        {
            Key = 1,
            Year = 2025,
            Description = "EMPTY",
            StartPayrollPeriod = new PayrollPeriodNumberModel { PeriodNumber = 1 },
            BaseType = null,
            StartEmployeeAgeType = null,
            StartEmployeeAge = 0.0m,
            EndEmployeeAgeType = null,
            EndEmployeeAge = 0.0m,
            ResultPayrollComponent = new KeyModel { Key = 257 },
            Percentage = 0.0m,
            CalculationPayrollPeriod = null,
            ReferencePayrollPeriod = null,
            PayoutPayrollPeriod = null,
            AccrualEndPayrollPeriod = null,
            PayslipType = new KeyModel { Key = 4 },
            IsPayoutAtStartOfEmployment = false,
            IsPayoutAtEndOfEmployment = false,
            AdvancePayrollComponent = null,
            AdvancePercentage = 0.0m,
            AdvancePayrollPeriod = null,
            PeriodicReservationPayrollComponent = null,
            FinancialReservationPercentage = 0.0m,
            FinancialMarkupPercentage = 0.0m,
            IsCumulativeCalculation = true,
            IsPartTimeCalculation = false,
            IsAutomaticCalculation = false,
            IsSupplementingDailyWage = false,
            MinimumMaximumType = new KeyModel { Key = 2 }
        };

        setNewPropertyValues?.Invoke(postModel);

        return postModel;
    }

    #endregion
}
