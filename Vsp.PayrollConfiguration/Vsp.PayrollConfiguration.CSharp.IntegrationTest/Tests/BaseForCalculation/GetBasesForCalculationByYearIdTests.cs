using Vsp.PayrollConfiguration.BaseForCalculation.Constants;
using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.BaseForCalculation;

[Collection(EntityNames.BaseForCalculation)]
public class GetBasesForCalculationByYearIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "BaseForCalculation";
    protected override bool UseTransaction => false;

    private static readonly Guid QA_BaseForCalculation_GET_CLA_2025 = Guid.Parse("000009a6-07e9-0000-0000-000000000000");
    private static readonly Guid QA_BaseForCalculation_GET_WM_2025 = Guid.Parse("000009a7-07e9-0000-0000-000000000000");
    private static readonly Guid QA_BaseForCalculation_GET_PA_2025 = Guid.Parse("000009a8-07e9-0000-0000-000000000000");

    private const string OrderBy = "orderBy=key,startPayrollPeriod.periodNumber";
    private const string OrderByDesc = "orderBy=-key,-startPayrollPeriod.periodNumber";
    private const string FilterByBaseForCalculationId = "filter=id eq '000009a8-07e9-0004-0100-000000000000'";
    private const string FilterByIsBaseTypeEqNull = "filter=baseType eq null";
    private const string FilterByIsSupplementingDailyWageEqTrue = "filter=isSupplementingDailyWage eq true";

    [Fact]
    public async Task Ok_QA_BaseForCalculation_GET_CLA_2025()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_CLA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_BaseForCalculation_GET_WM_2025()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_WM_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_QA_BaseForCalculation_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_PA_2025.ToString()) +
            $"?{OrderBy}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_OrderBy_Key_Desc_QA_BaseForCalculation_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_PA_2025.ToString()) +
            $"?{OrderByDesc}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_QA_BaseForCalculation_GET_PA_2025()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            $"&{FilterByBaseForCalculationId}";
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_BaseType_Null()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            $"&{FilterByIsBaseTypeEqNull}"; // nullable code table
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }

    [Fact]
    public async Task Ok_Filter_IsSupplementingDailyWage()
    {
        // Act
        var getUri =
            $"{BaseForCalculationRoutes.GetBasesForCalculationByYearIdAsync}"
                .Replace("{yearId:guid}", QA_BaseForCalculation_GET_PA_2025.ToString()) +
            $"?{OrderBy}" +
            $"&{FilterByIsSupplementingDailyWageEqTrue}"; // Yes (1) and No (2) mapping
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult);
    }
}