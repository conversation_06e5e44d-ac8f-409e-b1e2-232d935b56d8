using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.WageModel.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.WageModel;

[Collection(EntityNames.WageModel)]
public class GetWageModelByWageModelIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "WageModel";
    protected override bool UseTransaction => false;

    private const string WageModelIdForWm = "a77d7e45-2612-450a-86d0-485baecee6ad";
    private const string WageModelIdForPa = "5e010bed-611d-469b-a220-5253e3896f47";

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_WM_ForLevel_WM()
    {
        // Act
        var getUri =
            WageModelRoutes.GetWageModelByWageModelIdAsync.Replace(
                "{wageModelId:guid}",
                WageModelIdForWm);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }

    [Fact]
    public async Task Ok_QA_PayrollConfiguration1_WM_ForLevel_PA()
    {
        // Act
        var getUri =
            WageModelRoutes.GetWageModelByWageModelIdAsync.Replace(
                "{wageModelId:guid}",
                WageModelIdForPa);
        var getResult = await CallApiAsync(HttpMethod.Get, getUri, HttpStatusCode.OK);

        // Assert
        await VerifyJsonAsync(getResult, scrubDates: false);
    }
}