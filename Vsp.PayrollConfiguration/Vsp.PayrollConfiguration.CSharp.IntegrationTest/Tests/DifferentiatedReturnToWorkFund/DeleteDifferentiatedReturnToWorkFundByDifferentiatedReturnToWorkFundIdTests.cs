using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.DifferentiatedReturnToWorkFund.Constants;
using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.DifferentiatedReturnToWorkFund;

[Collection(EntityNames.DifferentiatedReturnToWorkFund)]
public class DeleteDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "DifferentiatedReturnToWorkFund";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_DifferentiatedReturnToWorkFund_DELETE_PA_2025 = Guid.Parse("00000926-07e9-0000-0000-000000000000");

    // In QA_DifferentiatedReturnToWorkFund_DELETE_PA
    private static readonly Guid DifferentiatedReturnToWorkFund_Period1 = Guid.Parse("22bd7777-4c52-4027-8a88-226b6947f102");
    private static readonly Guid DifferentiatedReturnToWorkFund_Period2 = Guid.Parse("14b17474-c0eb-4e86-9514-d1e8ccef0ee8");

    [Fact]
    public async Task Ok_QA_DifferentiatedReturnToWorkFund_DELETE_PA()
    {
        // Call DELETE endpoint
        await VerifyCallAsync(
            new Request
            {
                Url = $"{DifferentiatedReturnToWorkFundRoutes.DeleteDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                    .Replace("{differentiatedReturnToWorkFundId:guid}", DifferentiatedReturnToWorkFund_Period2.ToString()),
                Method = HttpMethod.Delete,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
                BodyValidation = JsonBodyValidation.Default,
            }
        );

        // Double-check result via GET endpoint
        var getResult = await VerifyCallAsync(
            new Request
            {
                Url = $"{DifferentiatedReturnToWorkFundRoutes.GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearIdAsync}"
                    .Replace("{yearId:guid}", QA_DifferentiatedReturnToWorkFund_DELETE_PA_2025.ToString())
                        + $"?filter=id eq '{DifferentiatedReturnToWorkFund_Period2}'",
                Method = HttpMethod.Get,
            },
            new ResponseValidation
            {
                ExpectedStatusCode = HttpStatusCode.OK,
            }
        );
        var getObject = JsonConvert.DeserializeObject<ListResult<DifferentiatedReturnToWorkFundModel>>(getResult!)!;
        getObject.Collection.Should().HaveCount(0);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodCannotBeDeleted()
    {
        // Act
        var deleteUri =
            $"{DifferentiatedReturnToWorkFundRoutes.DeleteDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundIdAsync}"
                .Replace("{differentiatedReturnToWorkFundId:guid}",
                    DifferentiatedReturnToWorkFund_Period1.ToString());
        var putResult = await CallApiAsync(HttpMethod.Delete, deleteUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(putResult);
    }
}