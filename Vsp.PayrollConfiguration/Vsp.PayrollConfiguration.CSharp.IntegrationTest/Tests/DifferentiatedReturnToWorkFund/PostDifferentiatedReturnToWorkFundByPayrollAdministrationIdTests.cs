using Vsp.PayrollConfiguration.CSharp.IntegrationTest.TestCollections;
using Vsp.PayrollConfiguration.DifferentiatedReturnToWorkFund.Constants;
using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.CSharp.IntegrationTest.Tests.DifferentiatedReturnToWorkFund;

[Collection(EntityNames.DifferentiatedReturnToWorkFund)]
public class PostDifferentiatedReturnToWorkFundByPayrollAdministrationIdTests(WebApplicationFactoryFixture<Program, ILoketContext, LoketContext> fixture)
    : CustomIntegrationTestsBase(fixture)
{
    protected override string FolderName => "DifferentiatedReturnToWorkFund";
    protected override bool UseTransaction => true;

    private static readonly Guid QA_DifferentiatedReturnToWorkFund_POST_PA = Guid.Parse("95bdd96f-31cd-4e2b-a02e-dc021264a6c2");

    [Fact]
    public async Task Ok_QA_DifferentiatedReturnToWorkFund_POST_PA()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = 2.321m, EmploymentContribution = 1.131m },
            Zw = new() { TotalContribution = 3.321m },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Assert
        await VerifyJsonAsync(postResult, scrubGuids: true);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_Null()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_Wga_Zw_Null()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel();
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_Wga_Zw_SubProperties_Null()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel { StartPayrollPeriod = new(), Wga = new(), Zw = new() };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_SubProperties_TooSmall()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 1899,
            StartPayrollPeriod = new() { PeriodNumber = 0 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    [Fact]
    public async Task BadRequest_ModelValidation_StartPayrollPeriod_SubProperties_TooBig()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 10000,
            StartPayrollPeriod = new() { PeriodNumber = 54 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Year_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Year_DoesNotExist()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2024,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_DoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_DoesNotExist()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 13 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_PayrollPeriod_FirstPeriodDoesNotExist"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_PayrollPeriod_FirstPeriodDoesNotExist()
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 2 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_Insert_Entity_AlreadyExists_CurrentInheritanceLevel"/>
    /// </summary>
    [Fact]
    public async Task BadRequest_MessageCode_Entity_AlreadyExists_CurrentInheritanceLevel()
    {
        // Arrange
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = 2, EmploymentContribution = 1 },
            Zw = new() { TotalContribution = 3 },
        };
        await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.Created);

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaZwTotalContributionOutOfRange"/>
    /// <para>NOTE: This comes from the PUT validator, but we want to check it's also activated for POST!</para>
    /// </summary>
    [Theory]
    [InlineData(99, 1)]
    [InlineData(-99, -1)]
    public async Task BadRequest_MessageCode_WgaZwTotalContributionsSumOutOfRange(decimal wgaTotalContribution, decimal zwTotalContribution)
    {
        // Act
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = wgaTotalContribution, EmploymentContribution = 0 },
            Zw = new() { TotalContribution = zwTotalContribution },
        };
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero"/>
    /// </summary>
    [Theory]
    [InlineData(0, 0.001)]
    [InlineData(-3, 0.001)]
    public async Task BadRequest_MessageCode_WgaTotalContributionZeroOrNegative_WgaEmploymentContributionNonZero(decimal wgaTotalContribution, decimal wgaEmploymentContribution)
    {
        // Arrange
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = wgaTotalContribution, EmploymentContribution = wgaEmploymentContribution },
            Zw = new() { TotalContribution = 3 },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }

    /// <summary>
    /// See: <see cref="MessageCodes.API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf"/>
    /// </summary>
    [Theory]
    [InlineData(50, 25.001)]
    [InlineData(10.691, 5.346)]
    public async Task BadRequest_MessageCode_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf(decimal wgaTotalContribution, decimal wgaEmploymentContribution)
    {
        // Arrange
        var postUri = $"{DifferentiatedReturnToWorkFundRoutes.PostDifferentiatedReturnToWorkFundsByPayrollAdministrationIdAsync}"
            .Replace("{payrollAdministrationId:guid}", QA_DifferentiatedReturnToWorkFund_POST_PA.ToString());
        var postModel = new DifferentiatedReturnToWorkFundPostModel
        {
            Year = 2025,
            StartPayrollPeriod = new() { PeriodNumber = 1 },
            Wga = new() { TotalContribution = wgaTotalContribution, EmploymentContribution = wgaEmploymentContribution },
            Zw = new() { TotalContribution = 3 },
        };

        // Act
        var postResult = await CallApiAsync(HttpMethod.Post, postUri, postModel, HttpStatusCode.BadRequest);

        // Assert
        await VerifyJsonAsync(postResult);
    }
}