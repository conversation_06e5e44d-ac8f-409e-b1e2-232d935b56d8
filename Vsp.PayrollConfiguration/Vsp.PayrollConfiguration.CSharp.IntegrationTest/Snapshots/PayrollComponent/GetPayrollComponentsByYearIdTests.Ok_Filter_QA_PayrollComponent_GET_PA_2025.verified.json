{"_embedded": [{"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 9, "value": "Netto betaling"}, "column": {"key": 13, "value": "<PERSON><PERSON> beta<PERSON>"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 3, "value": "PayrollAdministration"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 3, "value": "PayrollAdministration"}, "column": {"key": 3, "value": "PayrollAdministration"}, "costsEmployer": {"key": 3, "value": "PayrollAdministration"}, "deductionOrPayment": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "hoursIndication": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageZw": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationOvertime": {"key": 3, "value": "PayrollAdministration"}, "isFullTime": {"key": 3, "value": "PayrollAdministration"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 3, "value": "PayrollAdministration"}, "isPayment": {"key": 3, "value": "PayrollAdministration"}, "isTravelExpense": {"key": 3, "value": "PayrollAdministration"}, "paymentDescription": {"key": 3, "value": "PayrollAdministration"}, "paymentPeriod": {"key": 3, "value": "PayrollAdministration"}, "socialSecurityLiable": {"key": 3, "value": "PayrollAdministration"}, "suppressPrinting": {"key": 3, "value": "PayrollAdministration"}, "suppressPrintingAccumulations": {"key": 3, "value": "PayrollAdministration"}, "taxLiable": {"key": 3, "value": "PayrollAdministration"}}, "description": "VRY NETTO B_PA", "hoursIndication": null, "id": "00000961-07e9-012c-0000-000000000000", "inheritanceLevel": {"id": "82ac85be-082b-4b72-b719-5b47098a122b", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 300, "paymentDescription": null, "paymentPeriod": {"key": 7, "value": "Herberekenen bij gebroken periode"}, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}