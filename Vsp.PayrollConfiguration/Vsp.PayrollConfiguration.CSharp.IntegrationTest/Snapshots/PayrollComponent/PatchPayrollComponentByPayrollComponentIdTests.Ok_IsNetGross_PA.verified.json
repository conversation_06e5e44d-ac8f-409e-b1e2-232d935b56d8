{"content": {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": {"key": 1, "value": "Berekende bedragen in de grondslag"}, "category": {"key": 8, "value": "<PERSON><PERSON><PERSON> vergo<PERSON> ta<PERSON>"}, "column": {"key": 7, "value": "<PERSON>een belastingp<PERSON>tig loon in geld"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 3, "value": "PayrollAdministration"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 3, "value": "PayrollAdministration"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "IsNetGross", "hoursIndication": null, "id": "00000987-07e9-01ae-0000-000000000000", "inheritanceLevel": {"id": "ef1259b4-ef52-4e25-af8c-d34f293c8e8c", "type": {"key": 3, "value": "PayrollAdministration"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": true, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 430, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 3, "value": "Tariefloon +"}, "year": 2025}, "messages": [{"code": 0, "description": "socialSecurityLiable is empty while taxLiable is 1, 2, 3 or 4.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_SocialSecurityLiable_2", "messageType": 4, "properties": null, "type": "Warning"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}