{"messages": [{"code": 0, "description": "", "exception": null, "id": null, "messageCode": "ModelStateValidationError", "messageType": 0, "properties": "{\"isPayment\":\"The IsPayment field is required.\",\"isFullTime\":\"The IsFullTime field is required.\",\"isOvertime\":\"The IsOvertime field is required.\",\"description\":\"The Description field is required.\",\"isNetToGross\":\"The IsNetToGross field is required.\",\"isTravelExpense\":\"The IsTravelExpense field is required.\",\"suppressPrinting\":\"The SuppressPrinting field is required.\",\"isBaseForCalculationOvertime\":\"The IsBaseForCalculationOvertime field is required.\",\"suppressPrintingAccumulations\":\"The SuppressPrintingAccumulations field is required.\",\"isBaseForCalculationDailyWageZw\":\"The IsBaseForCalculationDailyWageZw field is required.\",\"isBaseForCalculationDailyWageSupplement\":\"The IsBaseForCalculationDailyWageSupplement field is required.\"}", "type": "BrokenBusinessRule"}], "resultObject": null, "success": false}