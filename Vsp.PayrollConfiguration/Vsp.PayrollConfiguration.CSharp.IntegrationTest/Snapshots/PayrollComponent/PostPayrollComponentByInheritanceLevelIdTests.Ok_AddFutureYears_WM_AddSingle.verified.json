{"content": {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 3, "value": "Aftrek alle heffingen"}, "costsEmployer": {"key": -1, "value": "-"}, "deductionOrPayment": {"key": 2, "value": "Inhouding"}, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "PREMIE FDS26", "hoursIndication": null, "id": "0000098f-07e8-07d1-0000-000000000000", "inheritanceLevel": {"id": "c8c71e3f-27e4-4b6b-abde-63254f24cc53", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 2001, "paymentDescription": null, "paymentPeriod": {"key": 7, "value": "Herberekenen bij gebroken periode"}, "socialSecurityLiable": {"key": 2, "value": "-"}, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 2, "value": "Tabelloon -"}, "year": 2024}, "messages": [{"code": 0, "description": "Automatically added entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedToFutureYear", "messageType": 6, "properties": "[2025]", "type": "Info"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}