{"content": null, "messages": [{"code": 0, "description": "Entity already exists on parent inheritance level.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AlreadyExists_ParentInheritanceLevel", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}, {"code": 0, "description": "Given payroll component is invalid. It is either unknown, or already exist on the given year of the inheritance level.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_PayrollComponent_Post_Invalid", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}