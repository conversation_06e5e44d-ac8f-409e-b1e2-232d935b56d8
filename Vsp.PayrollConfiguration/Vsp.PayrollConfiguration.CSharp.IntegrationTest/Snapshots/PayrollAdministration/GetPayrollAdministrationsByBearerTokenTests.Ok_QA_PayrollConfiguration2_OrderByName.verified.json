{"_embedded": [{"administrationNumber": null, "clientNumber": "101612", "collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA", "id": "9ad57900-980e-404a-b8a3-14eca3278552"}, "employer": {"companyName": "QA_PayrollConfiguration2", "id": "f22758a4-9475-41f0-9519-2070106b4267"}, "groupCode": 1337, "id": "8d20dffc-49b6-454c-93e1-574d6a4dcb06", "name": "QA_PayrollConfiguration2_PA_ForLevel_PA", "wageModel": {"collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA", "id": "9ad57900-980e-404a-b8a3-14eca3278552"}, "comment": "Empty WM for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration2_WM_ForLevel_PA", "id": "b2dd38be-45fa-4a64-b21e-df1911cc3e07"}}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}