{"content": {"accrualEndPayrollPeriod": null, "advancePayrollComponent": null, "advancePayrollPeriod": null, "advancePercentage": 0.0, "baseType": null, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseType": {"key": 1, "value": "CollectiveLaborAgreement"}, "calculationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "endEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialMarkupPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "financialReservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isAutomaticCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isCumulativeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPartTimeCalculation": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtEndOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayoutAtStartOfEmployment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isSupplementingDailyWage": {"key": 1, "value": "CollectiveLaborAgreement"}, "minimumMaximumType": {"key": 1, "value": "CollectiveLaborAgreement"}, "payoutPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "payslipType": {"key": 1, "value": "CollectiveLaborAgreement"}, "percentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "periodicReservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "referencePayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPayrollPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "reservationPercentage": {"key": 1, "value": "CollectiveLaborAgreement"}, "resultPayrollComponent": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAge": {"key": 1, "value": "CollectiveLaborAgreement"}, "startEmployeeAgeType": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "EMPTY", "endEmployeeAge": 0.0, "endEmployeeAgeType": null, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009d0-07e8-0001-0100-000000000000", "inheritanceLevel": {"id": "de79d924-9ff4-4e02-887b-20f2b4db6042", "type": {"key": 1, "value": "CollectiveLaborAgreement"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": true, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 1, "minimumMaximumType": {"key": 2, "value": "Resultaat"}, "payoutPayrollPeriod": null, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 0.0, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN GEWERKT", "key": 1}, "startEmployeeAge": 0.0, "startEmployeeAgeType": null, "startPayrollPeriod": {"payrollPeriodId": 202401, "periodEndDate": "2024-01-31", "periodNumber": 1, "periodStartDate": "2024-01-01", "year": 2024}, "year": 2024}, "messages": [{"code": 0, "description": "Automatically added arrangement payroll component(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedArrangementPayrollComponent", "messageType": 6, "properties": "[210, 633, 190, 637, 641, 645, 649, 653, 657, 661, 835, 845]", "type": "Info"}, {"code": 0, "description": "Automatically added entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedToFutureYear", "messageType": 6, "properties": "[2025]", "type": "Info"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}