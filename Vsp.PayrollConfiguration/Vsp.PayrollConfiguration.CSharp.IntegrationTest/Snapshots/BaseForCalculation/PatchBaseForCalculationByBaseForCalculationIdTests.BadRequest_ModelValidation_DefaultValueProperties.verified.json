{"messages": [{"code": 0, "description": "", "exception": null, "id": null, "messageCode": "ModelStateValidationError", "messageType": 0, "properties": "{\"percentage\":\"The Percentage field is required.\",\"description\":\"The Description field is required.\",\"payslipType\":\"The PayslipType field is required.\",\"endEmployeeAge\":\"The EndEmployeeAge field is required.\",\"startEmployeeAge\":\"The StartEmployeeAge field is required.\",\"advancePercentage\":\"The AdvancePercentage field is required.\",\"minimumMaximumType\":\"The MinimumMaximumType field is required.\",\"isPartTimeCalculation\":\"The IsPartTimeCalculation field is required.\",\"isAutomaticCalculation\":\"The IsAutomaticCalculation field is required.\",\"resultPayrollComponent\":\"The ResultPayrollComponent field is required.\",\"isCumulativeCalculation\":\"The IsCumulativeCalculation field is required.\",\"isSupplementingDailyWage\":\"The IsSupplementingDailyWage field is required.\",\"financialMarkupPercentage\":\"The FinancialMarkupPercentage field is required.\",\"isPayoutAtEndOfEmployment\":\"The IsPayoutAtEndOfEmployment field is required.\",\"isPayoutAtStartOfEmployment\":\"The IsPayoutAtStartOfEmployment field is required.\",\"financialReservationPercentage\":\"The FinancialReservationPercentage field is required.\"}", "type": "BrokenBusinessRule"}], "resultObject": null, "success": false}