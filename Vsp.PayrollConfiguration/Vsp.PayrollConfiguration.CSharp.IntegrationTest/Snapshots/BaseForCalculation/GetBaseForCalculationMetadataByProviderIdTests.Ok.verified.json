{"content": {"baseType": [{"key": 1, "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"key": 2, "value": "Arbeidsvw.bedrag (vh extra periodesalaris)"}], "employeeAgeType": [{"key": 1, "value": "Eerste van de periode VAN bereiken leeftijd"}, {"key": 2, "value": "Eerste van de periode NA bereiken leeftijd"}, {"key": 3, "value": "Op het bereiken van leeftijd"}, {"key": 4, "value": "Eerste van de periode VAN bereiken AOW-leeftijd"}, {"key": 5, "value": "Eerste van de periode NA bereiken AOW-leeftijd"}, {"key": 6, "value": "Op het bere<PERSON><PERSON> van A<PERSON>-leeftijd"}, {"key": 7, "value": "Eerste van de maand VAN bereiken leeftijd"}, {"key": 8, "value": "Eerste van de maand NA bereiken leeftijd"}], "minimumMaximumType": [{"key": 1, "value": "Grondslag"}, {"key": 2, "value": "Resultaat"}], "payslipType": [{"key": 4, "value": "Normale strook"}, {"key": 5, "value": "Bijzonder tarief strook"}]}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}