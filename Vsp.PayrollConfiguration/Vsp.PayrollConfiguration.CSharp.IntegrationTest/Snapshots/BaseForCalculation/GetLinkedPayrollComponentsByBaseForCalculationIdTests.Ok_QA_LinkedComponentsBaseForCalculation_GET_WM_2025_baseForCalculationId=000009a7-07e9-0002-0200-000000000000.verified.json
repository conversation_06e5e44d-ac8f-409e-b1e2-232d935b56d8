{"_embedded": [{"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "BEREKEN GRSL2", "hoursIndication": null, "id": "000009a7-07e9-00bf-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 191, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "MUTATIE GRSL2", "hoursIndication": null, "id": "000009a7-07e9-00d3-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 211, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 7, "value": "<PERSON><PERSON><PERSON> vergo<PERSON> tabel"}, "column": {"key": 1, "value": "Loon in geld"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": {"key": 1, "value": "<PERSON><PERSON>"}, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "VRY BRTBET.TAB", "hoursIndication": null, "id": "000009a7-07e9-0121-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 289, "paymentDescription": null, "paymentPeriod": {"key": 7, "value": "Herberekenen bij gebroken periode"}, "socialSecurityLiable": {"key": 1, "value": "+"}, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": {"key": 1, "value": "Tabelloon +"}, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 26, "value": "Output"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "DEELTIJD GRSL2", "hoursIndication": null, "id": "000009a7-07e9-027a-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 634, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "CUM GRSL2", "hoursIndication": null, "id": "000009a7-07e9-027e-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 638, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "CUM PRES GRSL2", "hoursIndication": null, "id": "000009a7-07e9-0282-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 642, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "CUM DT GRSL2", "hoursIndication": null, "id": "000009a7-07e9-0286-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 646, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": true, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "RES.FIN.GRSL2", "hoursIndication": null, "id": "000009a7-07e9-028a-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 650, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "AAN RES.GRSL2", "hoursIndication": null, "id": "000009a7-07e9-028e-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 654, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 1, "value": "Debet"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": {"key": 1, "value": "+"}, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "OPSL.KST.GRSL2", "hoursIndication": null, "id": "000009a7-07e9-0292-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 658, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": {"key": 2, "value": "Credit"}, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "AAN OPSL.GRSL2", "hoursIndication": null, "id": "000009a7-07e9-0296-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 662, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": true, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 14, "value": "Grondslagen"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "TE BET GRSL2", "hoursIndication": null, "id": "000009a7-07e9-0344-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 836, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 98, "value": "Intern"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 2, "value": "WageModel"}, "baseForCalculationBter": {"key": 2, "value": "WageModel"}, "category": {"key": 2, "value": "WageModel"}, "column": {"key": 2, "value": "WageModel"}, "costsEmployer": {"key": 2, "value": "WageModel"}, "deductionOrPayment": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "hoursIndication": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageSupplement": {"key": 2, "value": "WageModel"}, "isBaseForCalculationDailyWageZw": {"key": 2, "value": "WageModel"}, "isBaseForCalculationOvertime": {"key": 2, "value": "WageModel"}, "isFullTime": {"key": 2, "value": "WageModel"}, "isNetToGross": {"key": 2, "value": "WageModel"}, "isOvertime": {"key": 2, "value": "WageModel"}, "isPayment": {"key": 2, "value": "WageModel"}, "isTravelExpense": {"key": 2, "value": "WageModel"}, "paymentDescription": {"key": 2, "value": "WageModel"}, "paymentPeriod": {"key": 2, "value": "WageModel"}, "socialSecurityLiable": {"key": 2, "value": "WageModel"}, "suppressPrinting": {"key": 2, "value": "WageModel"}, "suppressPrintingAccumulations": {"key": 2, "value": "WageModel"}, "taxLiable": {"key": 2, "value": "WageModel"}}, "description": "UIT BET GRSL2 ", "hoursIndication": null, "id": "000009a7-07e9-034e-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 846, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}, {"balanceSheetSide": null, "baseForCalculationBter": null, "category": {"key": 3, "value": "Overwerk"}, "column": {"key": 0, "value": "<PERSON><PERSON> bruto-netto"}, "costsEmployer": null, "deductionOrPayment": null, "definedAtLevel": {"balanceSheetSide": {"key": 1, "value": "CollectiveLaborAgreement"}, "baseForCalculationBter": {"key": 1, "value": "CollectiveLaborAgreement"}, "category": {"key": 1, "value": "CollectiveLaborAgreement"}, "column": {"key": 1, "value": "CollectiveLaborAgreement"}, "costsEmployer": {"key": 1, "value": "CollectiveLaborAgreement"}, "deductionOrPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "description": {"key": 1, "value": "CollectiveLaborAgreement"}, "hoursIndication": {"key": 1, "value": "CollectiveLaborAgreement"}, "id": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageSupplement": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationDailyWageZw": {"key": 1, "value": "CollectiveLaborAgreement"}, "isBaseForCalculationOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isFullTime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isNetToGross": {"key": 1, "value": "CollectiveLaborAgreement"}, "isOvertime": {"key": 1, "value": "CollectiveLaborAgreement"}, "isPayment": {"key": 1, "value": "CollectiveLaborAgreement"}, "isTravelExpense": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentDescription": {"key": 1, "value": "CollectiveLaborAgreement"}, "paymentPeriod": {"key": 1, "value": "CollectiveLaborAgreement"}, "socialSecurityLiable": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrinting": {"key": 1, "value": "CollectiveLaborAgreement"}, "suppressPrintingAccumulations": {"key": 1, "value": "CollectiveLaborAgreement"}, "taxLiable": {"key": 1, "value": "CollectiveLaborAgreement"}}, "description": "UREN OVW(100%)", "hoursIndication": null, "id": "000009a7-07e9-0fb4-0000-000000000000", "inheritanceLevel": {"id": "df3a7673-df27-4a20-9077-26dcef52ddfd", "type": {"key": 2, "value": "WageModel"}}, "isBaseForCalculationDailyWageSupplement": false, "isBaseForCalculationDailyWageZw": false, "isBaseForCalculationOvertime": false, "isFullTime": false, "isNetToGross": false, "isOvertime": false, "isPayment": false, "isTravelExpense": false, "key": 4020, "paymentDescription": null, "paymentPeriod": null, "socialSecurityLiable": null, "suppressPrinting": false, "suppressPrintingAccumulations": false, "taxLiable": null, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 14, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}