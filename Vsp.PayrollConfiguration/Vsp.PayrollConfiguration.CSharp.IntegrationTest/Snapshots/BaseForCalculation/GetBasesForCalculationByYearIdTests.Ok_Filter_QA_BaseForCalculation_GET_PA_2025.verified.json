{"_embedded": [{"accrualEndPayrollPeriod": null, "advancePayrollComponent": null, "advancePayrollPeriod": null, "advancePercentage": 0.0, "baseType": null, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "baseType": {"key": 3, "value": "PayrollAdministration"}, "calculationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "description": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}, "financialMarkupPercentage": {"key": 3, "value": "PayrollAdministration"}, "financialReservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 3, "value": "PayrollAdministration"}, "isCumulativeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtStartOfEmployment": {"key": 3, "value": "PayrollAdministration"}, "isSupplementingDailyWage": {"key": 3, "value": "PayrollAdministration"}, "minimumMaximumType": {"key": 3, "value": "PayrollAdministration"}, "payoutPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "payslipType": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}, "periodicReservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "referencePayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollPeriod": {"key": 3, "value": "PayrollAdministration"}, "reservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "resultPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}}, "description": "BFC_PA_4_1", "endEmployeeAge": 0.0, "endEmployeeAgeType": null, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009a8-07e9-0004-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": true, "isCumulativeCalculation": false, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": false, "key": 4, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 4, "value": "Normale strook"}, "percentage": 0.0, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "VERLOONDE UREN", "key": 498}, "startEmployeeAge": 0.0, "startEmployeeAgeType": null, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}