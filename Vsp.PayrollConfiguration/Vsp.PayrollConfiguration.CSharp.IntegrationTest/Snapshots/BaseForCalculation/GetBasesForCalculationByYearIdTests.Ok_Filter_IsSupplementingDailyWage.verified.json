{"_embedded": [{"accrualEndPayrollPeriod": null, "advancePayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN SPEC.BEZ.", "key": 22}, "advancePayrollPeriod": null, "advancePercentage": 2.222001, "baseType": {"key": 2, "value": "Arbeidsvw.bedrag (vh extra periodesalaris)"}, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 2, "value": "WageModel"}, "baseType": {"key": 2, "value": "WageModel"}, "calculationPayrollPeriod": {"key": 2, "value": "WageModel"}, "description": {"key": 2, "value": "WageModel"}, "endEmployeeAge": {"key": 2, "value": "WageModel"}, "endEmployeeAgeType": {"key": 2, "value": "WageModel"}, "financialMarkupPercentage": {"key": 2, "value": "WageModel"}, "financialReservationPercentage": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}, "isAutomaticCalculation": {"key": 2, "value": "WageModel"}, "isCumulativeCalculation": {"key": 2, "value": "WageModel"}, "isPartTimeCalculation": {"key": 2, "value": "WageModel"}, "isPayoutAtEndOfEmployment": {"key": 2, "value": "WageModel"}, "isPayoutAtStartOfEmployment": {"key": 2, "value": "WageModel"}, "isSupplementingDailyWage": {"key": 2, "value": "WageModel"}, "minimumMaximumType": {"key": 2, "value": "WageModel"}, "payoutPayrollPeriod": {"key": 2, "value": "WageModel"}, "payslipType": {"key": 2, "value": "WageModel"}, "percentage": {"key": 2, "value": "WageModel"}, "periodicReservationPayrollComponent": {"key": 2, "value": "WageModel"}, "referencePayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPayrollComponent": {"key": 2, "value": "WageModel"}, "reservationPayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPercentage": {"key": 2, "value": "WageModel"}, "resultPayrollComponent": {"key": 2, "value": "WageModel"}, "startEmployeeAge": {"key": 2, "value": "WageModel"}, "startEmployeeAgeType": {"key": 2, "value": "WageModel"}}, "description": "BFC_WM_2_1", "endEmployeeAge": 0.75, "endEmployeeAgeType": {"key": 3, "value": "Op het bereiken van leeftijd"}, "financialMarkupPercentage": 2.000001, "financialReservationPercentage": 2.222001, "id": "000009a8-07e9-0002-0100-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": false, "isPartTimeCalculation": false, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": true, "key": 2, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 5, "value": "Bijzonder tarief strook"}, "percentage": 2.000221, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 10, "value": "Netto/bruto"}, "description": "NETTO TOESL.1", "key": 128}, "startEmployeeAge": 0.5, "startEmployeeAgeType": {"key": 3, "value": "Op het bereiken van leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, {"accrualEndPayrollPeriod": null, "advancePayrollComponent": {"category": {"key": 7, "value": "<PERSON><PERSON><PERSON> vergo<PERSON> tabel"}, "description": "REISKST.BELAST", "key": 283}, "advancePayrollPeriod": null, "advancePercentage": 3.000003, "baseType": null, "calculationPayrollPeriod": null, "definedAtLevel": {"accrualEndPayrollPeriod": {"key": 2, "value": "WageModel"}, "baseType": {"key": 2, "value": "WageModel"}, "calculationPayrollPeriod": {"key": 2, "value": "WageModel"}, "description": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "endEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}, "financialMarkupPercentage": {"key": 2, "value": "WageModel"}, "financialReservationPercentage": {"key": 2, "value": "WageModel"}, "id": {"key": 3, "value": "PayrollAdministration"}, "isAutomaticCalculation": {"key": 2, "value": "WageModel"}, "isCumulativeCalculation": {"key": 2, "value": "WageModel"}, "isPartTimeCalculation": {"key": 3, "value": "PayrollAdministration"}, "isPayoutAtEndOfEmployment": {"key": 2, "value": "WageModel"}, "isPayoutAtStartOfEmployment": {"key": 2, "value": "WageModel"}, "isSupplementingDailyWage": {"key": 3, "value": "PayrollAdministration"}, "minimumMaximumType": {"key": 3, "value": "PayrollAdministration"}, "payoutPayrollPeriod": {"key": 2, "value": "WageModel"}, "payslipType": {"key": 3, "value": "PayrollAdministration"}, "percentage": {"key": 3, "value": "PayrollAdministration"}, "periodicReservationPayrollComponent": {"key": 2, "value": "WageModel"}, "referencePayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "reservationPayrollPeriod": {"key": 2, "value": "WageModel"}, "reservationPercentage": {"key": 3, "value": "PayrollAdministration"}, "resultPayrollComponent": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAge": {"key": 3, "value": "PayrollAdministration"}, "startEmployeeAgeType": {"key": 3, "value": "PayrollAdministration"}}, "description": "BFC_WM_2_2_OVERRIDES@PA", "endEmployeeAge": 0.5, "endEmployeeAgeType": {"key": 8, "value": "Eerste van de maand NA bereiken leeftijd"}, "financialMarkupPercentage": 0.0, "financialReservationPercentage": 0.0, "id": "000009a8-07e9-0002-0200-000000000000", "inheritanceLevel": {"id": "4685cf08-be12-4205-aefd-338d808123b5", "type": {"key": 3, "value": "PayrollAdministration"}}, "isAutomaticCalculation": false, "isCumulativeCalculation": false, "isPartTimeCalculation": true, "isPayoutAtEndOfEmployment": false, "isPayoutAtStartOfEmployment": false, "isSupplementingDailyWage": true, "key": 2, "minimumMaximumType": {"key": 1, "value": "Grondslag"}, "payoutPayrollPeriod": null, "payslipType": {"key": 5, "value": "Bijzonder tarief strook"}, "percentage": 3.000333, "periodicReservationPayrollComponent": null, "referencePayrollPeriod": null, "resultPayrollComponent": {"category": {"key": 1, "value": "<PERSON><PERSON>"}, "description": "UREN OPN OSV", "key": 4121}, "startEmployeeAge": 0.25, "startEmployeeAgeType": {"key": 7, "value": "Eerste van de maand VAN bereiken leeftijd"}, "startPayrollPeriod": {"payrollPeriodId": 202502, "periodEndDate": "2025-02-28", "periodNumber": 2, "periodStartDate": "2025-02-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 2, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}