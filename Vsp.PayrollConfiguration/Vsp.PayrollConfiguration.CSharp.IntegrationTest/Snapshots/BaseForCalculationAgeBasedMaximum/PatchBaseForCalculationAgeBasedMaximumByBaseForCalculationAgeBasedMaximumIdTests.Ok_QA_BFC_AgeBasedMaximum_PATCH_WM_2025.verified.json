{"content": {"age": 5, "baseForCalculation": {"key": 1}, "definedAtLevel": {"id": {"key": 2, "value": "WageModel"}, "maximum": {"key": 2, "value": "WageModel"}}, "id": "000009f8-07e9-0001-0500-010000000000", "inheritanceLevel": {"id": "e857b85f-9487-49d1-bc52-57fee1cc5c28", "type": {"key": 2, "value": "WageModel"}}, "maximum": 999999.99, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}