{"_embedded": [{"age": 50, "baseForCalculation": {"key": 1}, "definedAtLevel": {"id": {"key": 2, "value": "WageModel"}, "maximum": {"key": 2, "value": "WageModel"}}, "id": "000009f2-07e9-0001-3200-0a0000000000", "inheritanceLevel": {"id": "80d1eee4-d554-40aa-b5ff-cc54cd85cfd5", "type": {"key": 2, "value": "WageModel"}}, "maximum": 50.0, "startPayrollPeriod": {"payrollPeriodId": 202510, "periodEndDate": "2025-10-31", "periodNumber": 10, "periodStartDate": "2025-10-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}