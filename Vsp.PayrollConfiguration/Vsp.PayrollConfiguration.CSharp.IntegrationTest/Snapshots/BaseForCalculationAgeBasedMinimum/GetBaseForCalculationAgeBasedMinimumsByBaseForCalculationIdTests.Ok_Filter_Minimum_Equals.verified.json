{"_embedded": [{"age": 40, "baseForCalculation": {"key": 1}, "definedAtLevel": {"id": {"key": 3, "value": "PayrollAdministration"}, "minimum": {"key": 3, "value": "PayrollAdministration"}}, "id": "000009de-07e9-0001-2800-010000000000", "inheritanceLevel": {"id": "d4db2331-6f1e-48b6-a360-22e55548aed6", "type": {"key": 3, "value": "PayrollAdministration"}}, "minimum": 40.15, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}