{"_embedded": [{"aof": {"key": 1, "value": "Kleine werkgever"}, "dateAvailableEss": "2025-04-17", "dateEssMail": null, "definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 3, "value": "PayrollAdministration"}, "standardShift": {"key": 3, "value": "PayrollAdministration"}}, "id": "0000093d-07e8-0000-0000-000000000000", "inheritanceLevel": {"id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "sendEssMail": false, "standardEmployeeProfile": {"description": null, "employeeProfileNumber": 99}, "standardShift": {"bonusPercentage": 0.0, "fullTimeHoursPerWeek": 0.0, "shiftNumber": 99}, "testYear": false, "year": 2024, "yearTransition": {"isPerformed": true, "isRequested": false, "performedDate": "2025-04-17"}, "zwSelfInsurerStartPayrollPeriod": null}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}