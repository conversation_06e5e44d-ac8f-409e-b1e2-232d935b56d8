{"content": {"aof": {"key": 2, "value": "(Middel) grote werkgever"}, "dateAvailableEss": "2025-04-30", "dateEssMail": null, "definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardShift": {"key": 1, "value": "CollectiveLaborAgreement"}}, "id": "00000947-07e6-0000-0000-000000000000", "inheritanceLevel": {"id": "92a092b0-d6c9-46dc-8d88-ce006cfbffb4", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollPeriodType": {"key": 1, "value": "<PERSON><PERSON>"}, "sendEssMail": false, "standardEmployeeProfile": null, "standardShift": {"bonusPercentage": 0.022, "fullTimeHoursPerWeek": 40.22, "shiftNumber": 1}, "testYear": true, "year": 2022, "yearTransition": {"isPerformed": true, "isRequested": false, "performedDate": "2025-04-30"}, "zwSelfInsurerStartPayrollPeriod": {"payrollPeriodId": 202201, "periodEndDate": "2022-01-31", "periodNumber": 1, "periodStartDate": "2022-01-01", "year": 2022}}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}