{"content": null, "messages": [{"code": 0, "description": "dateAvailableEss may only be changed when the current value is empty or in the future.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Year_DateAvailableEss_OldDateAvailableEssNotEmptyAndNotInFuture", "messageType": 0, "properties": null, "type": "BrokenBusinessRule"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}