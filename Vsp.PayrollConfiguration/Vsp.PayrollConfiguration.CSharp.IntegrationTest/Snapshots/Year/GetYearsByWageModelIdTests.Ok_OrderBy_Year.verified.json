{"_embedded": [{"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "0000093c-07e4-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": {"description": "WM employee profile 2020", "employeeProfileNumber": 1}, "standardShift": {"bonusPercentage": 15.025, "fullTimeHoursPerWeek": 15.25, "shiftNumber": 15}, "year": 2020}, {"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardShift": {"key": 1, "value": "CollectiveLaborAgreement"}}, "id": "0000093c-07e5-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": null, "standardShift": {"bonusPercentage": 0.021, "fullTimeHoursPerWeek": 40.21, "shiftNumber": 1}, "year": 2021}, {"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "0000093c-07e6-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": null, "standardShift": null, "year": 2022}, {"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "0000093c-07e7-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": {"description": "WM employee profile 2023", "employeeProfileNumber": 1}, "standardShift": {"bonusPercentage": 5.023, "fullTimeHoursPerWeek": 38.23, "shiftNumber": 2}, "year": 2023}, {"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "0000093c-07e8-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": {"description": "WM employee profile 2024", "employeeProfileNumber": 1}, "standardShift": {"bonusPercentage": 5.024, "fullTimeHoursPerWeek": 38.24, "shiftNumber": 2}, "year": 2024}, {"definedAtLevel": {"payrollPeriodType": {"key": 2, "value": "WageModel"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "0000093c-07e9-0000-0000-000000000000", "inheritanceLevel": {"id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": {"key": 2, "value": "WageModel"}}, "payrollPeriodType": {"key": 3, "value": "4 Weken"}, "standardEmployeeProfile": {"description": "WM employee profile 2025", "employeeProfileNumber": 1}, "standardShift": {"bonusPercentage": 5.025, "fullTimeHoursPerWeek": 38.25, "shiftNumber": 11}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 6, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}