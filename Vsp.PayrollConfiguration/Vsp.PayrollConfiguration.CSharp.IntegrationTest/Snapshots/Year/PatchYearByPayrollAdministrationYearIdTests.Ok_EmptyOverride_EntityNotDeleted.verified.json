{"content": {"aof": null, "dateAvailableEss": "2025-04-30", "dateEssMail": null, "definedAtLevel": {"payrollPeriodType": {"key": 1, "value": "CollectiveLaborAgreement"}, "standardEmployeeProfile": {"key": 2, "value": "WageModel"}, "standardShift": {"key": 2, "value": "WageModel"}}, "id": "00000947-07e4-0000-0000-000000000000", "inheritanceLevel": {"id": "92a092b0-d6c9-46dc-8d88-ce006cfbffb4", "type": {"key": 3, "value": "PayrollAdministration"}}, "payrollPeriodType": {"key": 1, "value": "<PERSON><PERSON>"}, "sendEssMail": false, "standardEmployeeProfile": {"description": "WM employee profile 2020", "employeeProfileNumber": 1}, "standardShift": null, "testYear": true, "year": 2020, "yearTransition": {"isPerformed": true, "isRequested": false, "performedDate": "2025-04-30"}, "zwSelfInsurerStartPayrollPeriod": {"payrollPeriodId": 202001, "periodEndDate": "2020-01-31", "periodNumber": 1, "periodStartDate": "2020-01-01", "year": 2020}}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}