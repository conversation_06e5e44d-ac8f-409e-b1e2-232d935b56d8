{"content": {"bonusPercentage": 6.0, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 6.0, "id": "Guid_1", "inheritanceLevel": {"id": "Guid_2", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 6, "startPayrollPeriod": {"payrollPeriodId": 202402, "periodEndDate": "2024-02-29", "periodNumber": 2, "periodStartDate": "2024-02-01", "year": 2024}, "year": 2024}, "messages": [{"code": 0, "description": "Automatically added entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedToFutureYear", "messageType": 6, "properties": "[2025]", "type": "Info"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}