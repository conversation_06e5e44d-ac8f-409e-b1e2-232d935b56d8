{"content": {"bonusPercentage": 10.5, "definedAtLevel": {"bonusPercentage": {"key": 2, "value": "WageModel"}, "fullTimeHoursPerWeek": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}}, "fullTimeHoursPerWeek": 1.23, "id": "Guid_1", "inheritanceLevel": {"id": "Guid_2", "type": {"key": 2, "value": "WageModel"}}, "shiftNumber": 1, "startPayrollPeriod": {"payrollPeriodId": 202001, "periodEndDate": "2020-01-31", "periodNumber": 1, "periodStartDate": "2020-01-01", "year": 2020}, "year": 2020}, "messages": [{"code": 0, "description": "Automatically added entity to future year(s) as well. See properties for details.", "exception": null, "id": null, "messageCode": "API_PayrollConfiguration_Insert_Entity_AddedToFutureYear", "messageType": 6, "properties": "[2021, 2022, 2023, 2024, 2025]", "type": "Info"}], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}