{"content": {"bonusPercentage": 19.63, "definedAtLevel": {"bonusPercentage": {"key": 2, "value": "WageModel"}, "fullTimeHoursPerWeek": {"key": 2, "value": "WageModel"}, "id": {"key": 2, "value": "WageModel"}}, "fullTimeHoursPerWeek": 15.31, "id": "00000950-07e9-0001-0100-000000000000", "inheritanceLevel": {"id": "2aa95c68-40b0-460c-9dce-0cde9be30ea0", "type": {"key": 2, "value": "WageModel"}}, "shiftNumber": 1, "startPayrollPeriod": {"payrollPeriodId": 202501, "periodEndDate": "2025-01-31", "periodNumber": 1, "periodStartDate": "2025-01-01", "year": 2025}, "year": 2025}, "messages": [], "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}