{"_embedded": [{"bonusPercentage": 42.0, "definedAtLevel": {"bonusPercentage": {"key": 3, "value": "PayrollAdministration"}, "fullTimeHoursPerWeek": {"key": 3, "value": "PayrollAdministration"}, "id": {"key": 3, "value": "PayrollAdministration"}}, "fullTimeHoursPerWeek": 42.0, "id": "0000094e-07e9-0004-0200-000000000000", "inheritanceLevel": {"id": "ef87f167-a7b7-4ee5-8d33-fb387737f39b", "type": {"key": 3, "value": "PayrollAdministration"}}, "shiftNumber": 4, "startPayrollPeriod": {"payrollPeriodId": 202502, "periodEndDate": "2025-02-28", "periodNumber": 2, "periodStartDate": "2025-02-01", "year": 2025}, "year": 2025}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 1, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}