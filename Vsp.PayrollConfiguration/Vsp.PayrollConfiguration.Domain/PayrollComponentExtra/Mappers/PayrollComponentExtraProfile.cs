using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Mappers;

internal class PayrollComponentExtraProfile : Profile
{
    public PayrollComponentExtraProfile()
    {
        CreateMap<Component, PayrollComponentExtraModel>()
            .ForMember(dest => dest.DeviatingDescription, opt => opt.MapFrom(src => src.VwComponentDeviantDescription == null ? null : src.VwComponentDeviantDescription.DeviatingDescription))
            .ForMember(dest => dest.RouteType, opt => opt.MapFrom(src => src.VwComponentDeviant == null ? null : src.VwComponentDeviant.CtRouteType))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Component, PayrollComponentExtraDefinedAtLevelModel>()
            .ForMember(dest => dest.DeviatingDescription,
                opt => opt.MapFrom(src => src.VwComponentDeviantDescription == null
                    ? null
                    : new InheritanceLevelTypeModel
                    {
                        Key = (InheritanceLevel)src.VwComponentDeviantDescription.DeviatingDescriptionDefinedAtLevel,
                        Value = ((InheritanceLevel)src.VwComponentDeviantDescription.DeviatingDescriptionDefinedAtLevel).ToString()
                    }))
            .ForMember(dest => dest.RouteType,
                opt => opt.MapFrom(src => src.VwComponentDeviant == null
                    ? null
                    : new InheritanceLevelTypeModel
                    {
                        Key = (InheritanceLevel)src.VwComponentDeviant.RouteTypeDefinedAtLevel,
                        Value = ((InheritanceLevel)src.VwComponentDeviant.RouteTypeDefinedAtLevel).ToString()
                    }));
    }
}