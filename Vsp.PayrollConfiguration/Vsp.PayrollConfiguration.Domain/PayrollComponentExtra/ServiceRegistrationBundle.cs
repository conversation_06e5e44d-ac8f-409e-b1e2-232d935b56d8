using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Commands;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Query;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Services;
using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IPayrollComponentExtraService, PayrollComponentExtraService>();
        services.AddScoped<IPatchPayrollComponentExtraCommand, PatchPayrollComponentExtraCommand>();
        services.AddScoped<IGetByIdQuery<PayrollComponentExtraModel, Component, ILoketContext>, GetPayrollComponentExtraQuery>();

        services.AddScoped<IValidator<PayrollComponentExtraPatchModel>, PatchPayrollComponentExtraValidator>();
    }
}