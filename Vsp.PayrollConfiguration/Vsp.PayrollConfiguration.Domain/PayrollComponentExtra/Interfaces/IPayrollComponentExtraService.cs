using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Interfaces;

public interface IPayrollComponentExtraService
{
    Task<IOperationResult<PayrollComponentExtraModel>> GetPayrollComponentExtraByPayrollComponentIdAsync(Guid payrollComponentId);
    Task<IOperationResult<PayrollComponentExtraModel>> PatchPayrollComponentExtraByPayrollComponentIdAsync(Guid payrollComponentId, PayrollComponentExtraPatchModel patchModel);
    Task<IOperationResult<PayrollComponentExtraMetadataModel>> GetPayrollComponentExtraMetadataByPayrollComponentIdAsync(Guid payrollComponentId);
}