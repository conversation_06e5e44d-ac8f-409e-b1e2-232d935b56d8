using Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Query;

public class GetPayrollComponentExtraQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetByIdQuery<PayrollComponentExtraModel, Component, ILoketContext>(dependencies)
{
    protected override Expression<Func<Component, bool>> IdIsExpression(Guid id) =>
        GeneratedIdHelper.ConstructWhere<Component>(id);
}