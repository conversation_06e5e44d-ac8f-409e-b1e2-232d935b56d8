using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Commands;

internal class InsertBaseForCalculationBasePayrollComponentCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<BaseForCalculationBasePayrollComponentPostModel, BaseForCalculationBasePayrollComponentModel, Repository.Entities.BaseForCalculationBasePayrollComponent, ModelBaseForCalculationBasePayrollComponent>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}