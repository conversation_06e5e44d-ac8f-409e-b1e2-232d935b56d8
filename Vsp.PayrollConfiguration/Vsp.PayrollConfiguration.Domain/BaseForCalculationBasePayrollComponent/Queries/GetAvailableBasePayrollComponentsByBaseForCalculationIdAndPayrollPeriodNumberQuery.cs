using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;

internal class GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery(
    IMapper mapper,
    IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper) : IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery
{
    public async Task<ListOperationResult<PayrollComponentMinimizedModel>> Execute(Guid baseForCalculationId, int payrollPeriodNumber)
    {
        var query = baseForCalculationBasePayrollComponentHelper
            .GetAvailableBasePayrollComponents(baseForCalculationId, payrollPeriodNumber)
            .ProjectTo<PayrollComponentMinimizedModel>(mapper.ConfigurationProvider);

        var result = await query.ToArrayAsync();
        return new ListOperationResult<PayrollComponentMinimizedModel>(result);
    }
}