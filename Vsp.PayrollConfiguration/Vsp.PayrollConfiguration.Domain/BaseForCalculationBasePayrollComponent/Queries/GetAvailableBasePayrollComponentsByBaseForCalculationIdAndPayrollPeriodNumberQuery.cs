using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;

internal class GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery(
    IMapper mapper,
    IAvailableComponentsHelper availableComponentsHelper) : IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery
{
    public async Task<ListOperationResult<PayrollComponentMinimizedModel>> Execute(Guid baseForCalculationId, int payrollPeriodNumber)
    {
        var query = availableComponentsHelper
            .GetAvailableBasePayrollComponents(baseForCalculationId, payrollPeriodNumber)
            .ProjectTo<PayrollComponentMinimizedModel>(mapper.ConfigurationProvider);

        var result = await query.ToArrayAsync();
        return new ListOperationResult<PayrollComponentMinimizedModel>(result);
    }
}