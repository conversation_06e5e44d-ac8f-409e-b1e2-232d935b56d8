using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;

public interface IBaseForCalculationBasePayrollComponentService
{
    Task<IListOperationResult<BaseForCalculationBasePayrollComponentModel>> GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync(Guid baseForCalculationId);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>> PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync(Guid baseForCalculationId, BaseForCalculationBasePayrollComponentPostModel postModel);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>> PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(Guid baseForCalculationBasePayrollComponentId, BaseForCalculationBasePayrollComponentPatchModel patchModel);
    Task<IOperationResult<NoResult>> DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(Guid baseForCalculationBasePayrollComponentId);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentMetadataModel>> GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync(Guid providerId);
    Task<IListOperationResult<PayrollComponentMinimizedModel>> GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync(Guid baseForCalculationId, int payrollPeriodNumber);
} 