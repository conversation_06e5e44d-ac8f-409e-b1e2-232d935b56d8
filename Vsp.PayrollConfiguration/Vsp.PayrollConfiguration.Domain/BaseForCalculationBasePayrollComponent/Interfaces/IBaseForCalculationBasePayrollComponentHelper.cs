using Vsp.PayrollConfiguration.Repository.Enums;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;

public interface IBaseForCalculationBasePayrollComponentHelper
{
    Task<bool> IsValidPayrollPeriodAsync(Guid baseForCalculationId, int payrollPeriodNumber);
    Task<Repository.Entities.BaseForCalculation> GetBaseForCalculationWithInheritanceLevelAsync(Guid baseForCalculationId);
    IQueryable<Repository.Entities.Component> GetAvailableBasePayrollComponents(
        Guid baseForCalculationId,
        int payrollPeriodNumber);
}
