using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Constants;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class InsertBaseForCalculationBasePayrollComponentValidator
    : AbstractValidator<BaseForCalculationBasePayrollComponentPostModel>
{
    public InsertBaseForCalculationBasePayrollComponentValidator(
        ILoketContext loketContext,
        IMapper mapper,
        IBaseForCalculationBasePayrollComponentHelper baseForCalculationBasePayrollComponentHelper
    )
    {
        Include(new InsertInheritanceEntityValidator<
            BaseForCalculationBasePayrollComponentPostModel,
            Repository.Entities.BaseForCalculationBasePayrollComponent,
            ModelBaseForCalculationBasePayrollComponent>(loketContext, mapper));

        // Business logic validation for PeriodNumber
        RuleFor(m => m.StartPayrollPeriod.PeriodNumber)
            .NotNull()
            .GreaterThan(0);

        // ---- Business rule: chosen component must be available for the given base + period ----
        RuleFor(m => m.PayrollComponent.Key)
            .MustAsync(async (model, componentId, token) =>
            {
                // If period/component not provided, let the presence rules handle it
                return model.StartPayrollPeriod?.PeriodNumber switch
                {
                    null => true,
                    var periodNumber => await baseForCalculationBasePayrollComponentHelper
                        .GetAvailableBasePayrollComponents(model.BaseForCalculationGuidId, periodNumber.Value)
                        .Where(c => c.ComponentId == componentId)
                        .AnyAsync(token)
                };
            })
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollComponent_Invalid)
            .WithMessage("The selected payroll component is not available for the specified base and payroll period.");

        // ---- Optional: ensure at least one component is available for this base + period ----
        RuleFor(m => m.StartPayrollPeriod.PeriodNumber)
            .MustAsync(async (model, periodNumber, token) =>
            {
                return periodNumber switch
                {
                    null => true, // presence rule will flag it
                    var value => await baseForCalculationBasePayrollComponentHelper
                        .GetAvailableBasePayrollComponents(model.BaseForCalculationGuidId, value.Value)
                        .AnyAsync(token)
                };
            })
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_PayrollPeriodNumber_Invalid)
            .WithMessage("No available components for the specified base and payroll period.");
    }
}