using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Validators;

internal class
    PatchBaseForCalculationBasePayrollComponentValidator : AbstractValidator<
    BaseForCalculationBasePayrollComponentPatchModel>
{
    private readonly ILoketContext loketContext;
    private readonly ICodeTableHelper codeTableHelper;
    private readonly IMapper mapper;

    public PatchBaseForCalculationBasePayrollComponentValidator(ILoketContext loketContext,
        ICodeTableHelper codeTableHelper, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.codeTableHelper = codeTableHelper;
        this.mapper = mapper;

        // Rule 1
        RuleFor(x => x)
            .MustAsync(async (x, _) => await BeValidCodeTableAsync<CtBaseOrigin>(x.Origin.Key!.Value))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid)
            .WithMessage("baseType.key is invalid");

        // Rule 2
        RuleFor(x => x)
            .MustAsync(async (x, token) => !await IsOriginIncompatibleWithNetToGross(x, token))
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_1)
            .WithMessage("Net-to-gross components from employee data may not be included in a base for calculation.");
    }

    private async Task<bool> BeValidCodeTableAsync<TCodeTable>(int key)
        where TCodeTable : CodeTable
    {
        var isValid = (await this.codeTableHelper.GetOptions<TCodeTable>()).Any(x => x.Key == key);
        return isValid;
    }

    private async Task<bool> IsOriginIncompatibleWithNetToGross(BaseForCalculationBasePayrollComponentPatchModel model, CancellationToken token)
        => model.Origin.Key == 1 && await IsNetToGrossComponent(model, token);

    private async Task<bool> IsNetToGrossComponent(BaseForCalculationBasePayrollComponentPatchModel model, CancellationToken token)
    {
        var isNetToGross = await this.loketContext.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>()
            .Include(bfcbpc => bfcbpc.Component)
            .AsNoTracking()
            .Where(
                GeneratedIdHelper.ConstructWhere<Repository.Entities.BaseForCalculationBasePayrollComponent>(
                    model.Id))
            .Select(bfcbpc => bfcbpc.Component.IsNetToGross)
            .ProjectTo<bool?>(this.mapper.ConfigurationProvider)
            .SingleOrDefaultAsync(token);

        if (isNetToGross != null)
        {
            return isNetToGross.Value;
        }

        throw new InvalidOperationException(
            "Unable to get current base for calculation base payroll component. You are trying to patch an unexisting entity.");
    }
}