using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Helpers;

public class AvailableComponentsHelper(ILoketContext context): IAvailableComponentsHelper
{
    public IQueryable<Repository.Entities.Component> GetAvailableBasePayrollComponents(
        Guid baseForCalculationId,
        int payrollPeriodNumber)
    {
        var baseForCalculation = new Repository.Entities.BaseForCalculation { Id = baseForCalculationId };
        GeneratedIdHelper.GenerateIdKeys(baseForCalculation);

        var bfcbpcSet = context.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>();

        var componentsAddedToBaseForCalculationQuery =
            bfcbpcSet.Where(bfcbpc =>
                bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                bfcbpc.YearId == baseForCalculation.YearId &&
                bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId);

        var query =
            context.Set<Repository.Entities.Component>()
                .Where(c =>
                    c.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                    c.YearId == baseForCalculation.YearId)
                .GroupJoin(
                    componentsAddedToBaseForCalculationQuery,
                    c => c.ComponentId,
                    bfcbpc => bfcbpc.ComponentId,
                    (c, bfcbpc) => new { c, bfcbpc })
                .SelectMany(
                    group => group.bfcbpc.DefaultIfEmpty(),
                    (group, bfcbpc) => new { group.c, bfcbpc })
                .Where(x =>
                    (payrollPeriodNumber == 1 && x.bfcbpc == null) ||
                    (payrollPeriodNumber > 1 &&
                     x.bfcbpc != null &&
                     bfcbpcSet.Count(bfcbpc =>
                         bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                         bfcbpc.YearId == baseForCalculation.YearId &&
                         bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId &&
                         bfcbpc.ComponentId == x.c.ComponentId &&
                         (bfcbpc.PayrollPeriodId == 1 || bfcbpc.PayrollPeriodId == payrollPeriodNumber)) == 1 && // The component is defined only in one period...
                     bfcbpcSet.Any(bfcbpc =>
                         bfcbpc.InheritanceLevelId == baseForCalculation.InheritanceLevelId &&
                         bfcbpc.YearId == baseForCalculation.YearId &&
                         bfcbpc.BaseForCalculationId == baseForCalculation.BaseForCalculationId &&
                         bfcbpc.ComponentId == x.c.ComponentId &&
                         bfcbpc.PayrollPeriodId == 1) // ... and that period is period 1.
                ))
                .Select(x => x.c);

        return query;
    }
}