using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Helpers;

public class BaseForCalculationBasePayrollComponentHelper(ILoketContext loketContext)
    : IBaseForCalculationBasePayrollComponentHelper
{
    public async Task<bool> IsValidPayrollPeriodAsync(Guid baseForCalculationId, int payrollPeriodNumber) =>
        await loketContext.Set<Repository.Entities.BaseForCalculation>()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.BaseForCalculation>(baseForCalculationId))
            .Where(bfc => bfc.Year.PayrollPeriods.Select(pp => pp.PayrollPeriodId).Contains(payrollPeriodNumber))
            .AnyAsync();

    public async Task<Repository.Entities.BaseForCalculation> GetBaseForCalculationWithInheritanceLevelAsync(Guid baseForCalculationId) =>
        await loketContext.Set<Repository.Entities.BaseForCalculation>()
            .AsNoTracking()
            .Include(bfc => bfc.InheritanceLevel)
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.BaseForCalculation>(baseForCalculationId))
            .SingleAsync();
}
