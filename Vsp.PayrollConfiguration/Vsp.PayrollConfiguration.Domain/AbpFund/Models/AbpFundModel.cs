using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Models;

public class AbpFundModel
{
    public Guid Id { get; set; }
    public InheritanceLevelModel InheritanceLevel { get; set; } = null!;
    public int Year { get; set; }
    public PayrollPeriodModel StartPayrollPeriod { get; set; } = null!;
    
    public int Key { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal TotalContribution { get; set; }
    public decimal EmploymentContribution { get; set; }
    public decimal Franchise { get; set; }
    public decimal FranchiseUpToAge40 { get; set; }
    public decimal FranchiseUpToAge50 { get; set; }
    
    public AbpFundDefinedAtLevelModel DefinedAtLevel { get; set; } = null!;
}

public class AbpFundDefinedAtLevelModel
{
    public InheritanceLevelTypeModel Id { get; set; } = null!;
    public InheritanceLevelTypeModel TotalContribution { get; set; } = null!;
    public InheritanceLevelTypeModel EmploymentContribution { get; set; } = null!;
    public InheritanceLevelTypeModel Franchise { get; set; } = null!;
    public InheritanceLevelTypeModel FranchiseUpToAge40 { get; set; } = null!;
    public InheritanceLevelTypeModel FranchiseUpToAge50 { get; set; } = null!;
}
