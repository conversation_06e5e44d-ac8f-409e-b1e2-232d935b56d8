using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Models;

public class AbpFundPatchModel : IPatchModel
{
    [JsonIgnore] 
    public Guid Id { get; set; }
    
    [Required]
    [Range(0, 99.999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,3})?$", ErrorMessage = "{0} must have at most 3 decimals.")]
    public decimal TotalContribution { get; set; }
    
    [Required]
    [Range(0, 99.999, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,3})?$", ErrorMessage = "{0} must have at most 3 decimals.")]
    public decimal EmploymentContribution { get; set; }
    
    [Required]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,2})?$", ErrorMessage = "{0} must have at most 2 decimals.")]
    public decimal Franchise { get; set; }
    
    [Required]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,2})?$", ErrorMessage = "{0} must have at most 2 decimals.")]
    public decimal FranchiseUpToAge40 { get; set; }
    
    [Required]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    [RegularExpression(@"^-?\d*([.,]\d{1,2})?$", ErrorMessage = "{0} must have at most 2 decimals.")]
    public decimal FranchiseUpToAge50 { get; set; }
}
