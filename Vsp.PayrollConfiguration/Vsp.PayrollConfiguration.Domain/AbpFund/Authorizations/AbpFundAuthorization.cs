using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Authorizations;

public class AbpFundAuthorizationModel
{
    public Guid AbpFundId { get; set; }
}

public class AbpFundAuthorization(ILoketContext loketContext)
    : InheritanceEntityAuthorization<AbpFundAuthorizationModel, Repository.Entities.AbpFund>(loketContext)
{
    protected override ResourceType ResourceType => ResourceType.FondsAbp;
    
    protected override Guid GetId(AbpFundAuthorizationModel authorizationObject) => authorizationObject.AbpFundId;
}
