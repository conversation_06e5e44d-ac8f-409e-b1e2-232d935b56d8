using Vsp.Infrastructure.EntityFilter;
using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Queries;

internal class GetAbpFundsQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<AbpFundModel, Repository.Entities.AbpFund, Repository.Entities.Year, ILoketContext>(dependencies)
{
    public override Expression<Func<Repository.Entities.AbpFund, bool>>? FilterCollectionByExpression(Guid id) =>
        GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Repository.Entities.AbpFund>(id, x => x.Year);
}
