using Vsp.PayrollConfiguration.Domain.AbpFund.Authorizations;
using Vsp.PayrollConfiguration.Domain.AbpFund.Commands;
using Vsp.PayrollConfiguration.Domain.AbpFund.Interfaces;
using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Domain.AbpFund.Queries;
using Vsp.PayrollConfiguration.Domain.AbpFund.Services;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<AbpFundAuthorizationModel>, AbpFundAuthorization>();
        services.AddScoped<IAbpFundService, AbpFundService>();
        services.AddScoped<IFilteredQuery<AbpFundModel, Repository.Entities.AbpFund, Repository.Entities.Year, ILoketContext>, GetAbpFundsQuery>();
        services.AddScoped<IGetInheritanceEntityQuery<AbpFundModel, Repository.Entities.AbpFund>, GetAbpFundQuery>();
        services.AddScoped<IInsertInheritanceEntityCommand<AbpFundPostModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund>, InsertAbpFundCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<AbpFundPatchModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund>, PatchAbpFundCommand>();
        services.AddScoped<IDeleteInheritanceEntityCommand<ModelAbpFund>, DeleteAbpFundCommand>();
    }
}
