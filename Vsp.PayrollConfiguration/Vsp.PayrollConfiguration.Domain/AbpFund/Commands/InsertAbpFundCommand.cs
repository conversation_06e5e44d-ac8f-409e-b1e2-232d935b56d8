using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Commands;

internal class InsertAbpFundCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<AbpFundModel, Repository.Entities.AbpFund> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<AbpFundPostModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}
