using Vsp.PayrollConfiguration.Domain.AbpFund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.AbpFund.Commands;

internal class PatchAbpFundCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<AbpFundModel, Repository.Entities.AbpFund> query)
    : PatchInheritanceEntityCommand<AbpFundPatchModel, AbpFundModel, Repository.Entities.AbpFund, ModelAbpFund>(dependencies, query)
{
    protected override bool UpdateOnly => false;
    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties = [];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}
