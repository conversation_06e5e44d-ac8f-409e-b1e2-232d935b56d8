namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;

/// <summary>
/// Details of an employer.
/// </summary>
public class EmployerModel
{
    /// <summary>
    /// The unique identifier of an employer (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the company.
    /// </summary>
    /// <example>Allure Bloemen en Planten</example>
    [JsonProperty(Required = Required.DisallowNull)]
    public string CompanyName { get; set; } = null!;
}
