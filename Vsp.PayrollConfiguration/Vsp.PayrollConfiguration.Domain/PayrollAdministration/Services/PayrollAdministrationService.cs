using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Interfaces;
using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Services;

internal class PayrollAdministrationService(
    ICurrentContext currentContext,
    IFilteredQuery<PayrollAdministrationModel, InheritanceLevel, InheritanceLevel, ILoketContext> getPayrollAdministrationsQuery,
    IGetByIdQuery<PayrollAdministrationModel, InheritanceLevel, ILoketContext> getByIdQuery
) : IPayrollAdministrationService
{
    private readonly ICurrentContext currentContext = currentContext;
    private readonly IFilteredQuery<PayrollAdministrationModel, InheritanceLevel, InheritanceLevel, ILoketContext> getPayrollAdministrationsQuery = getPayrollAdministrationsQuery;
    private readonly IGetByIdQuery<PayrollAdministrationModel, InheritanceLevel, ILoketContext> getByIdQuery = getByIdQuery;

    public async Task<IListOperationResult<PayrollAdministrationModel>> GetPayrollAdministrationsByBearerTokenAsync() =>
        await this.getPayrollAdministrationsQuery.ExecuteList(this.currentContext.UserId);

    public async Task<IOperationResult<PayrollAdministrationModel>> GetPayrollAdministrationByPayrollAdministrationIdAsync(Guid payrollAdministrationId) =>
        await this.getByIdQuery.Execute(payrollAdministrationId);
}
