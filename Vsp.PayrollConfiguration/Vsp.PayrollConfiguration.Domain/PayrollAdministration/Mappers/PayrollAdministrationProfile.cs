using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Mappers;

internal class PayrollAdministrationProfile : Profile
{
    public PayrollAdministrationProfile()
    {
        CreateMap<InheritanceLevel, PayrollAdministrationModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.Name, opt => opt.MapFrom(src => src.Administration!.Name))
            .ForMember(dst => dst.AdministrationNumber, opt => opt.MapFrom(src => src.Administration!.AdministrationNumber))
            .ForMember(dst => dst.ClientNumber, opt => opt.MapFrom(src => src.Administration!.ClientNumber))
            .ForMember(dst => dst.GroupCode, opt => opt.MapFrom(src => src.Administration != null ? src.Administration.GroupCode : 0))
            .ForMember(dst => dst.Employer, opt => opt.MapFrom(src => src.Administration!.Employer))
            .ForMember(dst => dst.WageModel, opt => opt.MapFrom(src => src.ParentInheritanceLevel!))
            .ForMember(dst => dst.CollectiveLaborAgreement, opt => opt.MapFrom(src => src.ParentInheritanceLevel!.ParentInheritanceLevel!));

        CreateMap<Administration, PayrollAdministrationModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.InheritanceLevel.Id))
            .ForMember(dst => dst.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dst => dst.AdministrationNumber, opt => opt.MapFrom(src => src.AdministrationNumber))
            .ForMember(dst => dst.ClientNumber, opt => opt.MapFrom(src => src.ClientNumber))
            .ForMember(dst => dst.GroupCode, opt => opt.MapFrom(src => src.GroupCode))
            .ForMember(dst => dst.Employer, opt => opt.MapFrom(src => src.Employer))
            .ForMember(dst => dst.WageModel, opt => opt.MapFrom(src => src.InheritanceLevel.ParentInheritanceLevel))
            .ForMember(dst => dst.CollectiveLaborAgreement, opt => opt.MapFrom(src => src.InheritanceLevel.ParentInheritanceLevel!.ParentInheritanceLevel));

        CreateMap<Employer, EmployerModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.CompanyName, opt => opt.MapFrom(src => src.Name));
    }
}
