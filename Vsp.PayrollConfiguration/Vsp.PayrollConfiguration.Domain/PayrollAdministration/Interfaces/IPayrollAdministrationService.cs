using Vsp.PayrollConfiguration.Domain.PayrollAdministration.Models;

namespace Vsp.PayrollConfiguration.Domain.PayrollAdministration.Interfaces;

public interface IPayrollAdministrationService
{
    Task<IListOperationResult<PayrollAdministrationModel>> GetPayrollAdministrationsByBearerTokenAsync();
    Task<IOperationResult<PayrollAdministrationModel>> GetPayrollAdministrationByPayrollAdministrationIdAsync(Guid payrollAdministrationId);
}
