using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;
using JournalLoket = Vsp.PayrollConfiguration.Repository.Entities.Loket.Journal;
using JournalUlsa = Vsp.PayrollConfiguration.Repository.Entities.Journal;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Validators;

internal class DeletePayrollComponentValidator : AbstractValidator<ModelComponent>
{
    private readonly ILoketContext loketContext;

    public DeletePayrollComponentValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;

        Include(new DeleteInheritanceEntityValidator<ModelComponent>(this.loketContext, mapper));

        WhenAsync(EntityIsDefinedOnCurrentInheritanceLevelAsync, () =>
        {
            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInPayrollPeriodDataOfThisYear_52001_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52001)
                .WithMessage("Payroll component is used in payroll period data of this year.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInExportSets_52002_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52002)
                .WithMessage("Payroll component is used in: payroll component sets (payroll administration level).");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInJournal_52003_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52003)
                .WithMessage("Payroll component is used in: journal (payroll administration level).");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInLedgerAccount_52004_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52004)
                .WithMessage("Payroll component is used in: ledger account.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInLedgerAccountToPayrollComponentMapping_52005_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52005)
                .WithMessage("Payroll component is used in: ledger account to payroll component mapping.");

            RuleFor(obj => obj)
                .MustAsync(NotBeLinkedAsExemptFromAttachmentOfEarnings_52006_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52006)
                .WithMessage("Payroll component is linked as exempt from attachment of earnings.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInUnitPercentage_52007_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52007)
                .WithMessage("Payroll component is used in: unit percentage.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInCalculationRule_52008_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52008)
                .WithMessage("Payroll component is used in: calculation rule.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInCalculationRuleDetails_52009_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52009)
                .WithMessage("Payroll component is used in: calculation rule details.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInEmployeeProfileScheme_52010_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52010)
                .WithMessage("Payroll component is used in: employee profile.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInCostPerHour_52011_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52011)
                .WithMessage("Payroll component is used in: cost per hour.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedAsABaseInFund_52012_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52012)
                .WithMessage("Payroll component is used as a base in: fund.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedAsABaseForCalculation_52013_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52013)
                .WithMessage("Payroll component is used as a base in: base for calculation.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedIn53rdWeek_52015_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52015)
                .WithMessage("Payroll component is used in: 53rd week.");

            RuleFor(obj => obj)
                .MustAsync(NotBeLinkedToAFund_52016_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52016)
                .WithMessage("Payroll component is linked to a fund.");

            RuleFor(obj => obj)
                .MustAsync(NotBeLinkedToABaseForCalculation_52017_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52017)
                .WithMessage("Payroll component is linked to a base for calculation.");

            RuleFor(obj => obj)
                .MustAsync(NotBeLinkedToABaseForCalculationAsResultAdvanceOrReservationComponent_52018_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52018)
                .WithMessage("Payroll component is linked to a base for calculation as a result, advance, or reservation component.");

            RuleFor(obj => obj)
                .MustAsync(NotBeLinkedToAnAbpFund_52019_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52019)
                .WithMessage("Payroll component is linked to an ABP fund.");

            RuleFor(obj => obj)
                .MustAsync(NotBeUsedInPayrolling_52021_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52021)
                .WithMessage("Payroll component has already been used in payrolling.");
        })
        .Otherwise(() =>
        {
            RuleFor(obj => obj)
                .MustAsync(NotDifferOnBalanceSheetSideForPALevel_52020_Async)
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_PayrollComponent_Delete_52020)
                .WithMessage("Payroll component balanceSheetSide differs from wage model level and has already been used in payrolling.");
        });
    }

    private async Task<bool> EntityIsDefinedOnCurrentInheritanceLevelAsync(ModelComponent component,
        CancellationToken token)
    {
        var claIds = this.loketContext.Set<InheritanceLevelInfo>()
            .Where(ili => ili.InheritanceLevelId == component.InheritanceLevelId)
            .Select(ili => ili.GrandParentInheritanceLevelId);

        var wnIds = this.loketContext.Set<InheritanceLevelInfo>()
            .Where(ili => ili.InheritanceLevelId == component.InheritanceLevelId)
            .Select(ili => ili.ParentInheritanceLevelId);

        var union = claIds.Union(wnIds);

        return !await union
            .Join(this.loketContext.Set<ModelComponent>(),
                c => c,
                mc => mc.InheritanceLevelId,
                (c, mc) => mc)
            .Where(mc =>
                  mc.YearId == component.YearId &&
                  mc.ComponentId == component.ComponentId)
            .AnyAsync(token);
    }

    private async Task<bool> NotBeUsedInPayrollPeriodDataOfThisYear_52001_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<Administration>(),
                mc => mc.InheritanceLevelId,
                a => a.InheritanceLevelId,
                (pc, a) => new { PayrollComponent = pc, Administration = a })
            .Join(this.loketContext.Set<Employment>(),
                combined => new { combined.Administration.EmployerId, combined.Administration.PayrollId },
                e => new { EmployerId = e.EmployerId, e.PayrollId, },
                (combined, e) => new { combined.PayrollComponent, combined.Administration, Employment = e })
            .Join(this.loketContext.Set<PayrollPeriodData>(),
                combined => new
                {
                    combined.Employment.EmployerId,
                    combined.Employment.PersonId,
                    combined.Employment.EmploymentId,
                    combined.PayrollComponent.YearId,
                },
                wt => new { EmployerId = wt.EmployerId, wt.PersonId, wt.EmploymentId, YearId = wt.PeriodId / 100 },
                (combined, wt) => new
                {
                    combined.PayrollComponent,
                    combined.Administration,
                    combined.Employment,
                    WageTransaction = wt
                })
            .Join(this.loketContext.Set<PayrollPeriodDataComponent>(),
                combined => new
                {
                    combined.WageTransaction.EmployerId,
                    combined.WageTransaction.PersonId,
                    combined.WageTransaction.EmploymentId,
                    combined.WageTransaction.WageTransactionId,
                    combined.PayrollComponent.ComponentId,
                },
                wtm => new
                {
                    EmployerId = wtm.EmployerId,
                    wtm.PersonId,
                    wtm.EmploymentId,
                    wtm.WageTransactionId,
                    wtm.ComponentId
                },
                (combined, wtm) => combined.PayrollComponent)
            .Where(pc => pc == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInExportSets_52002_Async(ModelComponent component,
        CancellationToken token) =>
    !await this.loketContext.Set<ModelComponent>()
        .Join(this.loketContext.Set<Administration>(),
            mc => mc.InheritanceLevelId,
            a => a.InheritanceLevelId,
            (pc, a) => new { PayrollComponent = pc, Administration = a })
        .Join(this.loketContext.Set<EmployerEssMutationSet>(),
            combined => new { combined.Administration.EmployerId, combined.Administration.PayrollId },
            ees => new { ees.EmployerId, ees.PayrollId },
            (combined, ees) => new { PayrollComponent = combined.PayrollComponent, ExportSet = ees })
        .Join(this.loketContext.Set<EmployerEssMutationSetDetail>(),
            combined => new
            {
                combined.ExportSet.EmployerId,
                combined.ExportSet.PayrollId,
                combined.ExportSet.EmployerExportSetId,
                combined.PayrollComponent.ComponentId
            },
            eesd => new
            {
                eesd.EmployerId,
                eesd.PayrollId,
                eesd.EmployerExportSetId,
                eesd.ComponentId
            },
            (combined, eesd) => combined.PayrollComponent)
        .Where(pc => pc.InheritanceLevelId == component.InheritanceLevelId &&
              pc.ComponentId == component.ComponentId &&
              pc.YearId == component.YearId)
    .AnyAsync(token);

    private async Task<bool> NotBeUsedInJournal_52003_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<Administration>(),
                mc => mc.InheritanceLevelId,
                a => a.InheritanceLevelId,
                (pc, a) => new { PayrollComponent = pc, Administration = a })
            .Join(this.loketContext.Set<JournalLoket>(),
                combined => new
                {
                    combined.Administration.EmployerId,
                    combined.Administration.PayrollId,
                    combined.PayrollComponent.ComponentId
                },
                j => new { j.EmployerId, j.PayrollId, j.ComponentId },
                (combined, j) => combined.PayrollComponent)
            .Where(pc => pc.InheritanceLevelId == component.InheritanceLevelId &&
                         pc.ComponentId == component.ComponentId &&
                         pc.YearId == component.YearId)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInLedgerAccount_52004_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ComponentGeneralLedgerAccount>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInLedgerAccountToPayrollComponentMapping_52005_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<JournalUlsa>(),
                mc => mc.InheritanceLevelId,
                j => j.InheritanceLevelId,
                (mc, j) => new { ModelComponent = mc, Journal = j })
            .Join(this.loketContext.Set<JournalProfile>(),
                combined => new
                {
                    combined.Journal.InheritanceLevelId,
                    combined.Journal.JournalProfileId
                },
                jp => new { jp.InheritanceLevelId, jp.JournalProfileId },
                (combined, j) => new { combined.ModelComponent, JournalProfile = j })
            .Join(this.loketContext.Set<LedgerAccountToPayrollComponentLink>(),
                combined => new { combined.JournalProfile.InheritanceLevelId, combined.JournalProfile.JournalProfileId, combined.ModelComponent.ComponentId },
                lapcl => new { lapcl.InheritanceLevelId, lapcl.JournalProfileId, lapcl.ComponentId },
                (combined, lapcl) => new
                {
                    combined.ModelComponent.InheritanceLevelId,
                    combined.ModelComponent.YearId,
                    combined.ModelComponent.ComponentId
                })
            .Where(joinsResult => joinsResult.InheritanceLevelId == component.InheritanceLevelId &&
                         joinsResult.ComponentId == component.ComponentId &&
                         joinsResult.YearId == component.YearId)
            .AnyAsync(token);

    private async Task<bool> NotBeLinkedAsExemptFromAttachmentOfEarnings_52006_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ComponentExemptFromAttachmentOfEarnings>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInUnitPercentage_52007_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<Repository.Entities.UnitPercentage>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInCalculationRule_52008_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<CalculationRule>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInCalculationRuleDetails_52009_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<CalculationRuleDetail>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInEmployeeProfileScheme_52010_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<EmployeeProfileArrangement>(),
                mc => new { mc.InheritanceLevelId, mc.YearId, ArrangementType = (int)ArrangementType.Benefit, Identification = mc.ComponentId },
                eps => new { eps.InheritanceLevelId, eps.YearId, eps.ArrangementType, eps.Identification },
                (mc, eps) => mc)
            .Where(mc => mc.InheritanceLevelId == component.InheritanceLevelId &&
                         mc.YearId == component.YearId &&
                         mc.ComponentId == component.ComponentId)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInCostPerHour_52011_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<CPHCalculationRuleDetail>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedAsABaseInFund_52012_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<FundBasePayrollComponent>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedAsABaseForCalculation_52013_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<Repository.Entities.BaseForCalculationBasePayrollComponent>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedIn53rdWeek_52015_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ComponentWeek53>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotBeLinkedToAFund_52016_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<Repository.Entities.Fund>(),
                mc => new { mc.InheritanceLevelId, mc.YearId },
                f => new { f.InheritanceLevelId, f.YearId },
                (mc, f) => new { Component = mc, f.FundId })
            .Join(this.loketContext.Set<ArrangementComponent>(),
                combined => new
                {
                    ArrangementType = (int)ArrangementType.Fund,
                    Identification = combined.FundId,
                    combined.Component.ComponentId
                },
                rc => new { rc.ArrangementType, rc.Identification, rc.ComponentId },
                (combined, rc) => combined.Component)
            .Where(mc => mc.InheritanceLevelId == component.InheritanceLevelId &&
                         mc.YearId == component.YearId &&
                         mc.ComponentId == component.ComponentId)
            .AnyAsync(token);

    private async Task<bool> NotBeLinkedToABaseForCalculation_52017_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<Repository.Entities.BaseForCalculation>(),
                mc => new { mc.InheritanceLevelId, mc.YearId },
                f => new { f.InheritanceLevelId, f.YearId },
                (mc, f) => new { Component = mc, FoundationId = f.BaseForCalculationId })
            .Join(this.loketContext.Set<ArrangementComponent>(),
                combined => new
                {
                    ArrangementType = (int)ArrangementType.BaseForCalculation,
                    Identification = combined.FoundationId,
                    combined.Component.ComponentId
                },
                rc => new { rc.ArrangementType, rc.Identification, rc.ComponentId },
                (combined, rc) => combined.Component)
            .Where(mc => mc.InheritanceLevelId == component.InheritanceLevelId &&
                         mc.YearId == component.YearId &&
                         mc.ComponentId == component.ComponentId)
            .AnyAsync(token);

    private async Task<bool> NotBeLinkedToABaseForCalculationAsResultAdvanceOrReservationComponent_52018_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>().Where(mc =>
                mc.InheritanceLevelId == component.InheritanceLevelId &&
                mc.YearId == component.YearId &&
                mc.ComponentId == component.ComponentId)
            .Join(this.loketContext.Set<Repository.Entities.BaseForCalculation>(),
                mc => new { mc.InheritanceLevelId, mc.YearId, mc.ComponentId },
                f => new
                {
                    f.InheritanceLevelId,
                    f.YearId,
                    ComponentId = f.ResultPayrollComponentId == component.ComponentId ? f.ResultPayrollComponentId :
                        f.AdvancePayrollComponentId == component.ComponentId ? f.AdvancePayrollComponentId :
                        f.PeriodicReservationPayrollComponentId == component.ComponentId ? f.PeriodicReservationPayrollComponentId :
                        -1
                },
                (mc, f) => new { mc.ComponentId })
            .AnyAsync(token);

    private async Task<bool> NotBeLinkedToAnAbpFund_52019_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<ModelComponent>()
            .Join(this.loketContext.Set<Repository.Entities.AbpFund>(),
                mc => new { mc.InheritanceLevelId, mc.YearId },
                f => new { f.InheritanceLevelId, f.YearId },
                (mc, f) => new { Component = mc, FundAbpId = f.AbpFundId })
            .Join(this.loketContext.Set<ArrangementComponent>(),
                combined => new
                {
                    ArrangementType = (int)ArrangementType.AbpFund,
                    Identification = combined.FundAbpId,
                    combined.Component.ComponentId
                },
                rc => new { rc.ArrangementType, rc.Identification, rc.ComponentId },
                (combined, rc) => combined.Component)
            .Where(mc => mc.InheritanceLevelId == component.InheritanceLevelId &&
                         mc.YearId == component.YearId &&
                         mc.ComponentId == component.ComponentId)
            .AnyAsync(token);

    private async Task<bool> NotBeUsedInPayrolling_52021_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<EmployeeWageResultComponent>()
            .Where(x => x.ModelComponent == component)
            .AnyAsync(token);

    private async Task<bool> NotDifferOnBalanceSheetSideForPALevel_52020_Async(ModelComponent component,
        CancellationToken token) =>
        !await this.loketContext.Set<InheritanceLevelInfo>()
            .Join(this.loketContext.Set<ModelComponent>(),
                ili => new { ili.InheritanceLevelId, component.YearId, component.ComponentId },
                mc => new { mc.InheritanceLevelId, mc.YearId, mc.ComponentId },
                (ili, mc) => new { InheritanceLevelInfo = ili, ModelComponent = mc })
            .Join(this.loketContext.Set<Component>(),
                combined => new
                {
                    InheritanceLevelId = combined.InheritanceLevelInfo.ParentInheritanceLevelId,
                    combined.ModelComponent.YearId,
                    combined.ModelComponent.ComponentId,
                },
                c => new { c.InheritanceLevelId, c.YearId, c.ComponentId },
                (combined, c) => new { combined.InheritanceLevelInfo, combined.ModelComponent, Component = c })
            .Where(combined => combined.ModelComponent.BalanceSheetSide != null &&
                               combined.ModelComponent.BalanceSheetSide != combined.Component.BalanceSheetSide)
            .Where(combined => combined.InheritanceLevelInfo.InheritanceLevelId == component.InheritanceLevelId &&
                               combined.InheritanceLevelInfo.Type == (int)InheritanceLevel.PayrollAdministration &&
                                this.loketContext.Set<EmployeeWageResultComponent>().Any(ewrc =>
                                    ewrc.InheritanceLevelId == combined.InheritanceLevelInfo.InheritanceLevelId &&
                                    ewrc.YearId == combined.ModelComponent.YearId &&
                                    ewrc.ComponentId == combined.ModelComponent.ComponentId))
            .AnyAsync(token);
}