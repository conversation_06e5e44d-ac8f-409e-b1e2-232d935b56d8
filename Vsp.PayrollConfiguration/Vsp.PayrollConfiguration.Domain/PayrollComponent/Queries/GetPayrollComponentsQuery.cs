using Vsp.Infrastructure.EntityFilter;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Queries;

internal class GetPayrollComponentsQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<PayrollComponentModel, Component, Repository.Entities.Year, ILoketContext>(dependencies)
{
    // TODO: DB filtering has been disabled because of problems with nullable code tables - how to fix?
    protected override IEntityFilter<PayrollComponentModel>? EntityFilter => null;

    public override Expression<Func<Component, bool>>? FilterCollectionByExpression(Guid id) =>
        GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Component>(id, x => x.Year);
}