using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Queries;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Queries;

internal class GetPayrollComponentQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : GetInheritanceEntityQuery<PayrollComponentModel, Component>(dependencies);