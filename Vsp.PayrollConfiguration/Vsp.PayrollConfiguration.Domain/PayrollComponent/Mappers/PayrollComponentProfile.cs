using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Mappers;

internal class PayrollComponentProfile : Profile
{
    public PayrollComponentProfile()
    {
        #region GET

        // GET
        CreateMap<Component, PayrollComponentModel>()
            .IncludeBase<Component, PayrollComponentMinimizedModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.CtDeductionOrPayment))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.CtPaymentPeriod))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.CtTaxLiable))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.CtSocialSecurityLiable))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.CtHoursIndication))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CtCostsEmployer))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGross))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZw))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplement))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.CtBaseForCalculationBter))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescription))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.CtColumn))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.CtBalanceSheetSide))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Component, PayrollComponentDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.ComponentIdDefinedAtLevel))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.DescriptionDefinedAtLevel))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPaymentDefinedAtLevel))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriodDefinedAtLevel))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiableDefinedAtLevel))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiableDefinedAtLevel))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndicationDefinedAtLevel))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployerDefinedAtLevel))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGrossDefinedAtLevel))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTimeDefinedAtLevel))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertimeDefinedAtLevel))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpenseDefinedAtLevel))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrintingDefinedAtLevel))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulationsDefinedAtLevel))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZwDefinedAtLevel))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplementDefinedAtLevel))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBterDefinedAtLevel))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPaymentDefinedAtLevel))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescriptionDefinedAtLevel))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertimeDefinedAtLevel))
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.ColumnDefinedAtLevel))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.BalanceSheetSideDefinedAtLevel))
            .ForMember(dst => dst.Category, opt => opt.MapFrom(src => src.CategoryDefinedAtLevel));

        #endregion

        #region POST

        // POST
        CreateMap<PayrollComponentExternalPostModel, PayrollComponentPostModel>()
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.Id, opt => opt.Ignore())

            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.Key))

            .ForMember(dst => dst.Description, opt => opt.Ignore())
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.Ignore())
            .ForMember(dst => dst.PaymentPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.TaxLiable, opt => opt.Ignore())
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.Ignore())
            .ForMember(dst => dst.HoursIndication, opt => opt.Ignore())
            .ForMember(dst => dst.CostsEmployer, opt => opt.Ignore())
            .ForMember(dst => dst.IsNetToGross, opt => opt.Ignore())
            .ForMember(dst => dst.IsFullTime, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.IsTravelExpense, opt => opt.Ignore())
            .ForMember(dst => dst.SuppressPrinting, opt => opt.Ignore())
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.Ignore())
            .ForMember(dst => dst.IsPayment, opt => opt.Ignore())
            .ForMember(dst => dst.PaymentDescription, opt => opt.Ignore())
            .ForMember(dst => dst.IsOvertime, opt => opt.Ignore())

            // Properties auto-calculated in ApplyPostProcessing()
            .ForMember(dst => dst.Column, opt => opt.Ignore())
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.Ignore())

            // Properties not in API but in DB
            .ForMember(dst => dst.Category, opt => opt.Ignore())
            .ForMember(dst => dst.GeneralLedgerAccountNumber, opt => opt.Ignore())
            .ForMember(dst => dst.IsLifeSpanScheme, opt => opt.Ignore())
            .ForMember(dst => dst.Order, opt => opt.Ignore())
            .ForMember(dst => dst.IsVisibleByDefault, opt => opt.Ignore());

        CreateMap<ComponentGeneral, PayrollComponentPostModel>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.Key, opt => opt.Ignore())

            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPayment == 0 ? null : new KeyModel { Key = src.DeductionOrPayment }))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriod == 0 ? null : new KeyModel { Key = src.PaymentPeriod }))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiable == 0 ? null : new KeyModel { Key = src.TaxLiable }))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiable == 0 ? null : new KeyModel { Key = src.SocialSecurityLiable }))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndication == 0 ? null : new KeyModel { Key = src.HoursIndication }))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployer == 0 ? null : new KeyModel { Key = src.CostsEmployer }))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => false))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => false))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => false))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBter == 0 ? null : new KeyModel { Key = src.BaseForCalculationBter }))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => (string?)null))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))

            // Properties auto-calculated in ApplyPostProcessing()
            .ForMember(dst => dst.Column, opt => opt.Ignore())
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.Ignore())

            // Properties not in API but in DB
            .ForMember(dst => dst.Category, opt => opt.MapFrom(src => src.Category))
            .ForMember(dst => dst.GeneralLedgerAccountNumber, opt => opt.MapFrom(src => (int?)null))
            .ForMember(dst => dst.IsLifeSpanScheme, opt => opt.MapFrom(src => src.IsLifeSpanScheme))
            .ForMember(dst => dst.Order, opt => opt.MapFrom(src => src.Order))
            .ForMember(dst => dst.IsVisibleByDefault, opt => opt.MapFrom(src => src.StandaardZichtbaar));

        CreateMap<PayrollComponentPostModel, ModelComponent>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items) ? (int)items[nameof(IInheritanceEntity.InheritanceLevelId)] : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year!.Value))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.Key))

            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPayment))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriod))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiable))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiable))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndication))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployer))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGross))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZw))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplement))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBter))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescription))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())

            // Navigation properties for audit trail
            .ForMember(dst => dst.CtDeductionOrPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtPaymentPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtTaxLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtSocialSecurityLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtHoursIndication, opt => opt.Ignore())
            .ForMember(dst => dst.CtCostsEmployer, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsNetToGross, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsFullTime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsTravelExpense, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrinting, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrintingAccumulations, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageZw, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageSupplement, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseForCalculationBter, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtColumn, opt => opt.Ignore())
            .ForMember(dst => dst.CtBalanceSheetSide, opt => opt.Ignore())
            .ForMember(dst => dst.CtCategory, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsLifeSpanScheme, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsVisibleByDefault, opt => opt.Ignore())

            .ForMember(dst => dst.ComponentLedgerAccount, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentWageGarnishmentExemptCompensation, opt => opt.Ignore())

            // Properties auto-calculated in ApplyPostProcessing()
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.Column))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.BalanceSheetSide))

            // Properties not in API but in DB
            .ForMember(dst => dst.Category, opt => opt.MapFrom(src => src.Category))
            .ForMember(dst => dst.GeneralLedgerAccountNumber, opt => opt.MapFrom(src => src.GeneralLedgerAccountNumber))
            .ForMember(dst => dst.IsLifeSpanScheme, opt => opt.MapFrom(src => src.IsLifeSpanScheme))
            .ForMember(dst => dst.Order, opt => opt.MapFrom(src => src.Order))
            .ForMember(dst => dst.IsVisibleByDefault, opt => opt.MapFrom(src => src.IsVisibleByDefault));

        #endregion

        #region PATCH

        // PATCH
        CreateMap<PayrollComponentPatchModel, Component>()
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPayment))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriod))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiable))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiable))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndication))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployer))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGross))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZw))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplement))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBter))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescription))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))

            // Properties auto-calculated in ApplyPostProcessing()
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.Column))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.BalanceSheetSide))

            // Properties not in API but in DB
            .ForMember(dst => dst.Category, opt => opt.Ignore())
            .ForMember(dst => dst.GeneralLedgerAccountNumber, opt => opt.Ignore())
            .ForMember(dst => dst.IsLifeSpanScheme, opt => opt.Ignore())
            .ForMember(dst => dst.Order, opt => opt.Ignore())
            .ForMember(dst => dst.IsVisibleByDefault, opt => opt.Ignore())

            // Ignore all other properties
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentId, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentIdDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.DescriptionDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.DeductionOrPaymentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PaymentPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.TaxLiableDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.SocialSecurityLiableDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.HoursIndicationDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.CostsEmployerDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsNetToGrossDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsFullTimeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationOvertimeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsTravelExpenseDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.SuppressPrintingDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.SuppressPrintingAccumulationsDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZwDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplementDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculationBterDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsPaymentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PaymentDescriptionDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsOvertimeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.ColumnDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.BalanceSheetSideDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.CategoryDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.CtDeductionOrPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtPaymentPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtTaxLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtSocialSecurityLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtHoursIndication, opt => opt.Ignore())
            .ForMember(dst => dst.CtCostsEmployer, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsNetToGross, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsFullTime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsTravelExpense, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrinting, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrintingAccumulations, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageZw, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageSupplement, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseForCalculationBter, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtColumn, opt => opt.Ignore())
            .ForMember(dst => dst.CtBalanceSheetSide, opt => opt.Ignore())
            .ForMember(dst => dst.CtCategory, opt => opt.Ignore())
            .ForMember(dst => dst.VwComponentDeviant, opt => opt.Ignore())
            .ForMember(dst => dst.VwComponentDeviantDescription, opt => opt.Ignore())
            .ForMember(dst => dst.ModelComponentDeviant, opt => opt.Ignore())
            .ForMember(dst => dst.ModelComponentDeviantDescription, opt => opt.Ignore());

        CreateMap<Component, ModelComponent>()
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.ComponentId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPayment))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriod))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiable))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiable))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndication))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployer))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGross))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZw))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplement))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBter))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescription))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.Column))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.BalanceSheetSide))

            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())

            // Navigation properties for audit trail
            .ForMember(dst => dst.CtDeductionOrPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtPaymentPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtTaxLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtSocialSecurityLiable, opt => opt.Ignore())
            .ForMember(dst => dst.CtHoursIndication, opt => opt.Ignore())
            .ForMember(dst => dst.CtCostsEmployer, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsNetToGross, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsFullTime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsTravelExpense, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrinting, opt => opt.Ignore())
            .ForMember(dst => dst.CtSuppressPrintingAccumulations, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageZw, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsBaseForCalculationDailyWageSupplement, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseForCalculationBter, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsOvertime, opt => opt.Ignore())
            .ForMember(dst => dst.CtColumn, opt => opt.Ignore())
            .ForMember(dst => dst.CtBalanceSheetSide, opt => opt.Ignore())
            .ForMember(dst => dst.CtCategory, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsLifeSpanScheme, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsVisibleByDefault, opt => opt.Ignore())

            .ForMember(dst => dst.ComponentLedgerAccount, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentWageGarnishmentExemptCompensation, opt => opt.Ignore())

            // Properties not in API but in DB
            .ForMember(dst => dst.Category, opt => opt.Ignore())
            .ForMember(dst => dst.GeneralLedgerAccountNumber, opt => opt.Ignore())
            .ForMember(dst => dst.IsLifeSpanScheme, opt => opt.Ignore())
            .ForMember(dst => dst.Order, opt => opt.Ignore())
            .ForMember(dst => dst.IsVisibleByDefault, opt => opt.Ignore());

        // Only used for PATCH integration tests
        CreateMap<PayrollComponentModel, PayrollComponentPatchModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.DeductionOrPayment, opt => opt.MapFrom(src => src.DeductionOrPayment))
            .ForMember(dst => dst.PaymentPeriod, opt => opt.MapFrom(src => src.PaymentPeriod))
            .ForMember(dst => dst.TaxLiable, opt => opt.MapFrom(src => src.TaxLiable))
            .ForMember(dst => dst.SocialSecurityLiable, opt => opt.MapFrom(src => src.SocialSecurityLiable))
            .ForMember(dst => dst.HoursIndication, opt => opt.MapFrom(src => src.HoursIndication))
            .ForMember(dst => dst.CostsEmployer, opt => opt.MapFrom(src => src.CostsEmployer))
            .ForMember(dst => dst.IsNetToGross, opt => opt.MapFrom(src => src.IsNetToGross))
            .ForMember(dst => dst.IsFullTime, opt => opt.MapFrom(src => src.IsFullTime))
            .ForMember(dst => dst.IsBaseForCalculationOvertime, opt => opt.MapFrom(src => src.IsBaseForCalculationOvertime))
            .ForMember(dst => dst.IsTravelExpense, opt => opt.MapFrom(src => src.IsTravelExpense))
            .ForMember(dst => dst.SuppressPrinting, opt => opt.MapFrom(src => src.SuppressPrinting))
            .ForMember(dst => dst.SuppressPrintingAccumulations, opt => opt.MapFrom(src => src.SuppressPrintingAccumulations))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageZw, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageZw))
            .ForMember(dst => dst.IsBaseForCalculationDailyWageSupplement, opt => opt.MapFrom(src => src.IsBaseForCalculationDailyWageSupplement))
            .ForMember(dst => dst.BaseForCalculationBter, opt => opt.MapFrom(src => src.BaseForCalculationBter))
            .ForMember(dst => dst.IsPayment, opt => opt.MapFrom(src => src.IsPayment))
            .ForMember(dst => dst.PaymentDescription, opt => opt.MapFrom(src => src.PaymentDescription))
            .ForMember(dst => dst.IsOvertime, opt => opt.MapFrom(src => src.IsOvertime))
            .ForMember(dst => dst.BalanceSheetSide, opt => opt.MapFrom(src => src.BalanceSheetSide))
            .ForMember(dst => dst.Column, opt => opt.MapFrom(src => src.Column));

        #endregion

        // For cloning objects with AutoMapper
        CreateMap<ModelComponent, ModelComponent>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentLedgerAccount, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentWageGarnishmentExemptCompensation, opt => opt.Ignore());
        CreateMap<PayrollComponentPostModel, PayrollComponentPostModel>();
    }
}