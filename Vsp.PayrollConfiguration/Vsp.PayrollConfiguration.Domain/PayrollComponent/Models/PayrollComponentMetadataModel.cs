using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;

/// <summary>
/// Details of payroll component metadata.
/// </summary>
public class PayrollComponentMetadataModel
{
    public List<KeyValueModel> DeductionOrPayment { get; set; } = null!;
    public List<KeyValueModel> PaymentPeriod { get; set; } = null!;
    public List<KeyValueModel> TaxLiable { get; set; } = null!;
    public List<KeyValueModel> SocialSecurityLiable { get; set; } = null!;
    public List<KeyValueModel> HoursIndication { get; set; } = null!;
    public List<KeyValueModel> CostsEmployer { get; set; } = null!;
    public List<KeyValueModel> BaseForCalculationBter { get; set; } = null!;
    public List<KeyValueModel> Column { get; set; } = null!;
    public List<KeyValueModel> BalanceSheetSide { get; set; } = null!;
    public List<KeyValueModel> Category { get; set; } = null!;
}