using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;

public class PayrollComponentModel : PayrollComponentMinimizedModel
{
    /// <summary>
    /// The unique identifier of a payroll component (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    public Guid Id { get; set; }

    public InheritanceLevelModel InheritanceLevel { get; set; } = null!;

    public int Year { get; set; }

    public KeyValueModel? DeductionOrPayment { get; set; } = null!;

    public KeyValueModel? PaymentPeriod { get; set; } = null!;

    public KeyValueModel? TaxLiable { get; set; } = null!;

    public KeyValueModel? SocialSecurityLiable { get; set; } = null!;

    public KeyValueModel? HoursIndication { get; set; } = null!;

    public KeyValueModel? CostsEmployer { get; set; } = null!;

    public bool IsNetToGross { get; set; }

    public bool IsFullTime { get; set; }

    public bool IsBaseForCalculationOvertime { get; set; }

    public bool IsTravelExpense { get; set; }

    public bool SuppressPrinting { get; set; }

    public bool SuppressPrintingAccumulations { get; set; }

    public bool IsBaseForCalculationDailyWageZw { get; set; }

    public bool IsBaseForCalculationDailyWageSupplement { get; set; }

    public KeyValueModel? BaseForCalculationBter { get; set; } = null!;

    public bool IsPayment { get; set; }

    public string? PaymentDescription { get; set; }

    public bool IsOvertime { get; set; }

    public KeyValueModel Column { get; set; } = null!;

    public KeyValueModel? BalanceSheetSide { get; set; } = null!;

    /// <summary>
    /// Describes at which level (CLA, WM, PA) the property is defined.
    /// </summary>
    public PayrollComponentDefinedAtLevelModel DefinedAtLevel { get; set; } = null!;
}

public class PayrollComponentDefinedAtLevelModel
{
    public InheritanceLevelTypeModel Id { get; set; } = null!;
    public InheritanceLevelTypeModel Description { get; set; } = null!;
    public InheritanceLevelTypeModel DeductionOrPayment { get; set; } = null!;
    public InheritanceLevelTypeModel PaymentPeriod { get; set; } = null!;
    public InheritanceLevelTypeModel TaxLiable { get; set; } = null!;
    public InheritanceLevelTypeModel SocialSecurityLiable { get; set; } = null!;
    public InheritanceLevelTypeModel HoursIndication { get; set; } = null!;
    public InheritanceLevelTypeModel CostsEmployer { get; set; } = null!;
    public InheritanceLevelTypeModel IsNetToGross { get; set; } = null!;
    public InheritanceLevelTypeModel IsFullTime { get; set; } = null!;
    public InheritanceLevelTypeModel IsBaseForCalculationOvertime { get; set; } = null!;
    public InheritanceLevelTypeModel IsTravelExpense { get; set; } = null!;
    public InheritanceLevelTypeModel SuppressPrinting { get; set; } = null!;
    public InheritanceLevelTypeModel SuppressPrintingAccumulations { get; set; } = null!;
    public InheritanceLevelTypeModel IsBaseForCalculationDailyWageZw { get; set; } = null!;
    public InheritanceLevelTypeModel IsBaseForCalculationDailyWageSupplement { get; set; } = null!;
    public InheritanceLevelTypeModel BaseForCalculationBter { get; set; } = null!;
    public InheritanceLevelTypeModel IsPayment { get; set; } = null!;
    public InheritanceLevelTypeModel PaymentDescription { get; set; } = null!;
    public InheritanceLevelTypeModel IsOvertime { get; set; } = null!;
    public InheritanceLevelTypeModel Column { get; set; } = null!;
    public InheritanceLevelTypeModel BalanceSheetSide { get; set; } = null!;
    public InheritanceLevelTypeModel Category { get; set; } = null!;
}