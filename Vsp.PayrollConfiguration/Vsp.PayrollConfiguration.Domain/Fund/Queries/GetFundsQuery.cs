using Vsp.Infrastructure.EntityFilter;
using Vsp.PayrollConfiguration.Domain.Fund.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Fund.Queries;

internal class GetFundsQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<FundModel, Repository.Entities.Fund, Repository.Entities.Year, ILoketContext>(dependencies)
{
    // TODO: DB filtering has been disabled because of problems with nullable code tables - how to fix?
    protected override IEntityFilter<FundModel>? EntityFilter => null;

    public override Expression<Func<Repository.Entities.Fund, bool>>? FilterCollectionByExpression(Guid id) =>
        GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Repository.Entities.Fund>(id, x => x.Year);
}