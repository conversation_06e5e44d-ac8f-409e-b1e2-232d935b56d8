using Vsp.PayrollConfiguration.Infrastructure.Authorizations;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Fund.Authorizations;

public class FundAuthorizationModel
{
    public Guid FundId { get; set; }
}

public class FundAuthorization(ILoketContext loketContext)
    : InheritanceEntityAuthorization<FundAuthorizationModel, Repository.Entities.Fund>(loketContext)
{
    protected override ResourceType ResourceType => ResourceType.Fonds;
    
    protected override Guid GetId(FundAuthorizationModel authorizationObject) => authorizationObject.FundId;
}