using Vsp.PayrollConfiguration.Domain.Fund.Interfaces;
using Vsp.PayrollConfiguration.Domain.Fund.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Fund.Services;

public class FundService(
    IFilteredQuery<FundModel, Repository.Entities.Fund, Repository.Entities.Year, ILoketContext> getQuery,
    IInsertInheritanceEntityCommand<FundPostModel, FundModel, Repository.Entities.Fund, ModelFund> insertCommand,
    IPatchInheritanceEntityCommand<FundPatchModel, FundModel, Repository.Entities.Fund, ModelFund> patchCommand,
    IDeleteInheritanceEntityCommand<ModelFund> deleteCommand)
    : IFundService
{
    private readonly IFilteredQuery<FundModel, Repository.Entities.Fund, Repository.Entities.Year, ILoketContext> getQuery = getQuery;
    private readonly IInsertInheritanceEntityCommand<FundPostModel, FundModel, Repository.Entities.Fund, ModelFund> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<FundPatchModel, FundModel, Repository.Entities.Fund, ModelFund> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelFund> deleteCommand = deleteCommand;

    public async Task<IListOperationResult<FundModel>> GetFundsByYearIdAsync(Guid yearId) =>
        await this.getQuery.ExecuteList(yearId);

    public async Task<IOperationResult<FundModel>> PostFundByInheritanceLevelIdAsync(Guid inheritanceLevelId, FundPostModel postModel)
    {
        // Remove the NotImplementedException when the associated command is ready
        throw new NotImplementedException();
        // postModel.InheritanceLevelId = inheritanceLevelId;
        // return await this.insertCommand.ExecuteAsync(postModel);
    }

    public async Task<IOperationResult<FundModel>> PatchFundByFundIdAsync(Guid fundId, FundPatchModel patchModel)
    {
        // Remove the NotImplementedException when the associated command is ready
        throw new NotImplementedException();
        // patchModel.Id = fundId;
        // return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>> DeleteFundByFundIdAsync(Guid fundId)
    {
        // Remove the NotImplementedException when the associated command is ready
        throw new NotImplementedException();
        // return await this.deleteCommand.ExecuteAsync(fundId);
    }

    public Task<IOperationResult<FundMetadataModel>> GetFundMetadataByProviderIdAsync() =>
        throw new NotImplementedException();

    public Task<IListOperationResult<PayrollComponentModel>> GetLinkedPayrollComponentsByFundIdAsync(Guid fundId) =>
        throw new NotImplementedException();
}