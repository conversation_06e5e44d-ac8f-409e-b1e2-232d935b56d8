using Vsp.PayrollConfiguration.Domain.Fund.Authorizations;
using Vsp.PayrollConfiguration.Domain.Fund.Commands;
using Vsp.PayrollConfiguration.Domain.Fund.Interfaces;
using Vsp.PayrollConfiguration.Domain.Fund.Models;
using Vsp.PayrollConfiguration.Domain.Fund.Queries;
using Vsp.PayrollConfiguration.Domain.Fund.Services;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Fund;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<FundAuthorizationModel>, FundAuthorization>();

        services.AddScoped<IFundService, FundService>();

        services.AddScoped<IFilteredQuery<FundModel, Repository.Entities.Fund, Repository.Entities.Year, ILoketContext>, GetFundsQuery>();
        services.AddScoped<IGetInheritanceEntityQuery<FundModel, Repository.Entities.Fund>, GetFundQuery>();
        services.AddScoped<IInsertInheritanceEntityCommand<FundPostModel, FundModel, Repository.Entities.Fund, ModelFund>, InsertFundCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<FundPatchModel, FundModel, Repository.Entities.Fund, ModelFund>, PatchFundCommand>();
        services.AddScoped<IDeleteInheritanceEntityCommand<ModelFund>, DeleteFundCommand>();

        // TODO: Add missing DI
    }
}