using Vsp.PayrollConfiguration.Domain.Fund.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Fund.Commands;

internal class PatchFundCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<FundModel, Repository.Entities.Fund> query)
    : PatchInheritanceEntityCommand<FundPatchModel, FundModel, Repository.Entities.Fund, ModelFund>(dependencies, query)
{
    protected override bool UpdateOnly => false;

    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties =
    [
        // TODO: Add properties to update
    ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}