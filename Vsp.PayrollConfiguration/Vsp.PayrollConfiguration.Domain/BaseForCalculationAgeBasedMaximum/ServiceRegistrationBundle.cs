using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Authorizations;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Commands;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Interfaces;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Queries;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Services;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<BaseForCalculationAgeBasedMaximumAuthorizationModel>, BaseForCalculationAgeBasedMaximumAuthorization>();
        services.AddScoped<IBaseForCalculationAgeBasedMaximumService, BaseForCalculationAgeBasedMaximumService>();

        services.AddScoped<IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum>, GetBaseForCalculationAgeBasedMaximumQuery>();

        services.AddScoped<IDeleteInheritanceEntityCommand<ModelBaseForCalculationAgeBasedMaximum>, DeleteBaseForCalculationAgeBasedMaximumCommand>();
        services.AddScoped<IInsertInheritanceEntityCommand<BaseForCalculationAgeBasedMaximumPostModel, BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum, ModelBaseForCalculationAgeBasedMaximum>, InsertBaseForCalculationAgeBasedMaximumCommand>();
        services.AddScoped<IPatchInheritanceEntityCommand<BaseForCalculationAgeBasedMaximumPatchModel, BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum, ModelBaseForCalculationAgeBasedMaximum>, PatchBaseForCalculationAgeBasedMaximumCommand>();
    }
}
