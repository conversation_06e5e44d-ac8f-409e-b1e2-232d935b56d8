using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;

public class BaseForCalculationAgeBasedMaximumPostModel : BaseForCalculationAgeBasedMaximumPatchModel, IPayrollPeriodPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }

    [Required]
    [Range(1900, 9999)]
    public int? Year { get; set; }   
    
    [Required]
    public KeyModel BaseForCalculation { get; set; } = null!;

    [Required]
    public int? Age { get; set; }   

    [Required]
    public PayrollPeriodNumberModel StartPayrollPeriod { get; set; } = null!;
}
