using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;

public class BaseForCalculationAgeBasedMaximumPatchModel: IPatchModel
{
    [JsonIgnore]
    public Guid Id { get; set; }

    [Required]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    public decimal? Maximum { get; set; }
}
