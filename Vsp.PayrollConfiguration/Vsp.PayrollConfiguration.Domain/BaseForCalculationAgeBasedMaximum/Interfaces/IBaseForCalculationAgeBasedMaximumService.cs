using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Interfaces;

public interface IBaseForCalculationAgeBasedMaximumService
{
    Task<IListOperationResult<BaseForCalculationAgeBasedMaximumModel>>
        GetBaseForCalculationAgeBasedMaximumsByBaseForCalculationIdAsync(Guid baseForCalculationId);

    Task<IOperationResult<BaseForCalculationAgeBasedMaximumModel>>
        PostBaseForCalculationAgeBasedMaximumByBaseForCalculationIdAsync(Guid baseForCalculationId,
            BaseForCalculationAgeBasedMaximumPostModel postModel);

    Task<IOperationResult<BaseForCalculationAgeBasedMaximumModel>>
        PatchBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync(
            Guid baseForCalculationAgeBasedMaximumId, BaseForCalculationAgeBasedMaximumPatchModel patchModel);

    Task<IOperationResult<NoResult>> DeleteBaseForCalculationAgeBasedMaximumByBaseForCalculationAgeBasedMaximumIdAsync(
        Guid baseForCalculationAgeBasedMaximumId);
}
