using Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMaximum.Commands;

public class InsertBaseForCalculationAgeBasedMaximumCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<BaseForCalculationAgeBasedMaximumPostModel, BaseForCalculationAgeBasedMaximumModel, Repository.Entities.BaseForCalculationAgeBasedMaximum, ModelBaseForCalculationAgeBasedMaximum>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}