using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;

public class DifferentiatedReturnToWorkFundPostModel : DifferentiatedReturnToWorkFundPutModel, IPayrollPeriodPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }
    
    /// <summary>
    /// The year number.
    /// </summary>
    /// <example>2025</example>
    [Required]
    [Range(1900, 9999)]
    public int? Year { get; set; } = null!;

    /// <summary>
    /// Start payroll period details for the differentiated return to work fund.
    /// </summary>
    [Required]
    public PayrollPeriodNumberModel StartPayrollPeriod { get; set; } = null!;
}