using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Queries;

internal class GetDifferentiatedReturnToWorkFundByYearIdQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<DifferentiatedReturnToWorkFundModel, Repository.Entities.DifferentiatedReturnToWorkFund, Repository.Entities.Year, ILoketContext>(dependencies)
{
    public override Expression<Func<Repository.Entities.DifferentiatedReturnToWorkFund, bool>> FilterCollectionByExpression(Guid id) =>
        GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Repository.Entities.DifferentiatedReturnToWorkFund>(id, x => x.Year);
}
