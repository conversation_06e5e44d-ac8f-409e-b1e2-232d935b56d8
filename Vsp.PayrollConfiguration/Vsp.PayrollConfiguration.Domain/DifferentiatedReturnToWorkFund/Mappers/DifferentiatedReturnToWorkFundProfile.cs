using Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Models;

namespace Vsp.PayrollConfiguration.Domain.DifferentiatedReturnToWorkFund.Mappers;

internal class DifferentiatedReturnToWorkFundProfile : Profile
{
    public DifferentiatedReturnToWorkFundProfile()
    {
        // GET
        CreateMap<Repository.Entities.DifferentiatedReturnToWorkFund, DifferentiatedReturnToWorkFundModel>()
            .ForMember(dst => dst.Wga, opt => opt.MapFrom(src => new WgaContributionModel
            {
                TotalContribution = src.WgaTotalContribution,
                EmploymentContribution = src.WgaEmploymentContribution
            }))
            .ForMember(dst => dst.Zw, opt => opt.MapFrom(src => new ZwContributionModel
            {
                TotalContribution = src.ZwTotalContribution
            }))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId));

        // POST
        CreateMap<DifferentiatedReturnToWorkFundPostModel, Repository.Entities.DifferentiatedReturnToWorkFund>()
            .ForMember(dst => dst.WgaTotalContribution, opt => opt.MapFrom(src => src.Wga.TotalContribution))
            .ForMember(dst => dst.WgaEmploymentContribution, opt => opt.MapFrom(src => src.Wga.EmploymentContribution))
            .ForMember(dst => dst.ZwTotalContribution, opt => opt.MapFrom(src => src.Zw.TotalContribution))
            .ForMember(dst => dst.InheritanceLevelId,
                opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items)
                    ? (int)items[nameof(DifferentiatedReturnToWorkFundPostModel.InheritanceLevelId)]
                    : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.StartPayrollPeriod.PeriodNumber))
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.Administration, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore());

        // PUT
        CreateMap<DifferentiatedReturnToWorkFundPutModel, Repository.Entities.DifferentiatedReturnToWorkFund>()
            .ForMember(dst => dst.WgaTotalContribution, opt => opt.MapFrom(src => src.Wga.TotalContribution))
            .ForMember(dst => dst.WgaEmploymentContribution, opt => opt.MapFrom(src => src.Wga.EmploymentContribution))
            .ForMember(dst => dst.ZwTotalContribution, opt => opt.MapFrom(src => src.Zw.TotalContribution))
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())
            .ForMember(dst => dst.Administration, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore());
    }
}