using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Interfaces;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Services;

internal class CollectiveLaborAgreementService(
    ICurrentContext currentContext,
    IFilteredQuery<CollectiveLaborAgreementModel, InheritanceLevel, InheritanceLevel, ILoketContext> getCollectiveLaborAgreementsQuery
) : ICollectiveLaborAgreementService
{
    public async Task<IListOperationResult<CollectiveLaborAgreementModel>> GetCollectiveLaborAgreementsByBearerTokenAsync() =>
            await getCollectiveLaborAgreementsQuery.ExecuteList(currentContext.UserId);

    public async Task<IOperationResult<CollectiveLaborAgreementModel>> GetCollectiveLaborAgreementByCollectiveLaborAgreementIdAsync(Guid collectiveLaborAgreementId) =>
        await getCollectiveLaborAgreementsQuery.Execute(collectiveLaborAgreementId);

}
