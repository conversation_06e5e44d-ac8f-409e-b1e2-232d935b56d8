using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Authorizations;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Interfaces;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Queries;
using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Services;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<CollectiveLaborAgreementAuthorizationModel>, CollectiveLaborAgreementAuthorization>();
        services.AddScoped<ICollectiveLaborAgreementService, CollectiveLaborAgreementService>();
        services.AddScoped<IFilteredQuery<CollectiveLaborAgreementModel, InheritanceLevel, InheritanceLevel, ILoketContext>, GetCollectiveLaborAgreementsQuery>();
    }
}
