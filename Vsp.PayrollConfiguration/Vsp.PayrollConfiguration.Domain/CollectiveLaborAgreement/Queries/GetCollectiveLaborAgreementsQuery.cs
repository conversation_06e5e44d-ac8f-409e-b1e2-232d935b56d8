using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Queries;

internal class GetCollectiveLaborAgreementsQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<CollectiveLaborAgreementModel, Repository.Entities.Base.InheritanceLevel, Repository.Entities.Base.InheritanceLevel, ILoketContext>(dependencies)
{
    public override Expression<Func<Repository.Entities.Base.InheritanceLevel, bool>>? FilterCollectionByExpression(Guid id) =>
        il => il.InheritanceLevelInfo.Type == (int)InheritanceLevel.CollectiveLaborAgreement; // CLA is the topmost level so we don't filter by user ID
}
