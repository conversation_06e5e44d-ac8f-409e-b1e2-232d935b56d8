using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Mappers;

internal class CollectiveLaborAgreementProfile : Profile
{
    public CollectiveLaborAgreementProfile() =>
        CreateMap<InheritanceLevel, CollectiveLaborAgreementModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description ?? ""))
            .ForMember(dst => dst.Comment, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.Comment) ? src.Comment : null));
}
