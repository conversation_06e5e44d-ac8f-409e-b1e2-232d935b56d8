using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Commands;

internal class PatchBaseForCalculationCommand(
    IBaseCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<BaseForCalculationModel, Repository.Entities.BaseForCalculation> query)
    : PatchInheritanceEntityCommand<BaseForCalculationPatchModel, BaseForCalculationModel, Repository.Entities.BaseForCalculation, ModelBaseForCalculation>(dependencies, query)
{
    protected override bool UpdateOnly => false;

    private static readonly IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> properties =
    [
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.Description))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.Description))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.BaseType))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.BaseType))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.StartEmployeeAgeType))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.StartEmployeeAgeType))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.StartEmployeeAge))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.StartEmployeeAge))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.EndEmployeeAgeType))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.EndEmployeeAgeType))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.EndEmployeeAge))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.EndEmployeeAge))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.ResultPayrollComponentId))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.ResultPayrollComponentId))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.Percentage))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.Percentage))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.CalculationPayrollPeriodNumber))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.CalculationPayrollPeriodNumber))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.ReferencePayrollPeriodNumber))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.ReferencePayrollPeriodNumber))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.PayoutPayrollPeriodNumber))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.PayoutPayrollPeriodNumber))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.AccrualEndPayrollPeriodNumber))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.AccrualEndPayrollPeriodNumber))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.PayslipType))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.PayslipType))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsPayoutAtStartOfEmployment))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsPayoutAtStartOfEmployment))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsPayoutAtEndOfEmployment))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsPayoutAtEndOfEmployment))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.AdvancePayrollComponentId))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.AdvancePayrollComponentId))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.AdvancePercentage))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.AdvancePercentage))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.AdvancePayrollPeriodNumber))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.AdvancePayrollPeriodNumber))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.PeriodicReservationPayrollComponentId))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.PeriodicReservationPayrollComponentId))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.FinancialReservationPercentage))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.FinancialReservationPercentage))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.FinancialMarkupPercentage))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.FinancialMarkupPercentage))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsCumulativeCalculation))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsCumulativeCalculation))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsPartTimeCalculation))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsPartTimeCalculation))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsAutomaticCalculation))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsAutomaticCalculation))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.IsSupplementingDailyWage))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.IsSupplementingDailyWage))! ),
        ( typeof(Repository.Entities.BaseForCalculation).GetProperty(nameof(Repository.Entities.BaseForCalculation.MinimumMaximumType))!, typeof(ModelBaseForCalculation).GetProperty(nameof(ModelBaseForCalculation.MinimumMaximumType))! )
    ];

    protected override IEnumerable<(PropertyInfo Entity, PropertyInfo ModelEntity)> Properties => properties;
}
