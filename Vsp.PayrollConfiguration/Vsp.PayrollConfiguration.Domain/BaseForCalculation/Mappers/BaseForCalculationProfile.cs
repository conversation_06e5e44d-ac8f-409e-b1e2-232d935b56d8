using Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Mappers;

internal class BaseForCalculationProfile : Profile
{
    public BaseForCalculationProfile()
    {
        #region GET

        CreateMap<Repository.Entities.BaseForCalculation, BaseForCalculationModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.BaseType, opt => opt.MapFrom(src => src.BaseType == 0 ? null : src.CtBaseType))
            .ForMember(dst => dst.StartEmployeeAgeType, opt => opt.MapFrom(src => src.StartEmployeeAgeType == 0 ? null : src.CtStartEmployeeAgeType))
            .ForMember(dst => dst.StartEmployeeAge, opt => opt.MapFrom(src => src.StartEmployeeAge))
            .ForMember(dst => dst.EndEmployeeAgeType, opt => opt.MapFrom(src => src.EndEmployeeAgeType == 0 ? null : src.CtEndEmployeeAgeType))
            .ForMember(dst => dst.EndEmployeeAge, opt => opt.MapFrom(src => src.EndEmployeeAge))
            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.MapFrom(src => src.ResultPayrollComponent))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculationPayrollPeriod, opt => opt.MapFrom(src => src.CalculationPayrollPeriodNumber == 0 ? null : new PayrollPeriodNumberModel { PeriodNumber = src.CalculationPayrollPeriodNumber }))
            .ForMember(dst => dst.ReferencePayrollPeriod, opt => opt.MapFrom(src => src.ReferencePayrollPeriodNumber == 0 ? null : new PayrollPeriodNumberModel { PeriodNumber = src.ReferencePayrollPeriodNumber }))
            .ForMember(dst => dst.PayoutPayrollPeriod, opt => opt.MapFrom(src => src.PayoutPayrollPeriodNumber == 0 ? null : new PayrollPeriodNumberModel { PeriodNumber = src.PayoutPayrollPeriodNumber }))
            .ForMember(dst => dst.AccrualEndPayrollPeriod, opt => opt.MapFrom(src => src.AccrualEndPayrollPeriodNumber == 0 ? null : new PayrollPeriodNumberModel { PeriodNumber = src.AccrualEndPayrollPeriodNumber }))
            .ForMember(dst => dst.PayslipType, opt => opt.MapFrom(src => src.CtPayslipType))
            .ForMember(dst => dst.IsPayoutAtStartOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtStartOfEmployment))
            .ForMember(dst => dst.IsPayoutAtEndOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtEndOfEmployment))
            .ForMember(dst => dst.AdvancePayrollComponent, opt => opt.MapFrom(src => src.AdvancePayrollComponent))
            .ForMember(dst => dst.AdvancePercentage, opt => opt.MapFrom(src => src.AdvancePercentage))
            .ForMember(dst => dst.AdvancePayrollPeriod, opt => opt.MapFrom(src => src.AdvancePayrollPeriodNumber == 0 ? null : new PayrollPeriodNumberModel { PeriodNumber = src.AdvancePayrollPeriodNumber }))
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.MapFrom(src => src.PeriodicReservationPayrollComponent))
            .ForMember(dst => dst.FinancialReservationPercentage, opt => opt.MapFrom(src => src.FinancialReservationPercentage))
            .ForMember(dst => dst.FinancialMarkupPercentage, opt => opt.MapFrom(src => src.FinancialMarkupPercentage))
            .ForMember(dst => dst.IsCumulativeCalculation, opt => opt.MapFrom(src => src.IsCumulativeCalculation))
            .ForMember(dst => dst.IsPartTimeCalculation, opt => opt.MapFrom(src => src.IsPartTimeCalculation))
            .ForMember(dst => dst.IsAutomaticCalculation, opt => opt.MapFrom(src => src.IsAutomaticCalculation))
            .ForMember(dst => dst.IsSupplementingDailyWage, opt => opt.MapFrom(src => src.IsSupplementingDailyWage))
            .ForMember(dst => dst.MinimumMaximumType, opt => opt.MapFrom(src => src.CtMinimumMaximumType))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.BaseForCalculation, BaseForCalculationDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.BaseForCalculationIdDefinedAtLevel))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.DescriptionDefinedAtLevel))
            .ForMember(dst => dst.BaseType, opt => opt.MapFrom(src => src.BaseTypeDefinedAtLevel))
            .ForMember(dst => dst.StartEmployeeAgeType, opt => opt.MapFrom(src => src.StartEmployeeAgeTypeDefinedAtLevel))
            .ForMember(dst => dst.StartEmployeeAge, opt => opt.MapFrom(src => src.StartEmployeeAgeDefinedAtLevel))
            .ForMember(dst => dst.EndEmployeeAgeType, opt => opt.MapFrom(src => src.EndEmployeeAgeTypeDefinedAtLevel))
            .ForMember(dst => dst.EndEmployeeAge, opt => opt.MapFrom(src => src.EndEmployeeAgeDefinedAtLevel))
            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.MapFrom(src => src.ResultPayrollComponentDefinedAtLevel))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.PercentageDefinedAtLevel))
            .ForMember(dst => dst.CalculationPayrollPeriod, opt => opt.MapFrom(src => src.CalculationPayrollPeriodDefinedAtLevel))
            .ForMember(dst => dst.ReferencePayrollPeriod, opt => opt.MapFrom(src => src.ReferencePayrollPeriodDefinedAtLevel))
            .ForMember(dst => dst.PayoutPayrollPeriod, opt => opt.MapFrom(src => src.PayoutPayrollPeriodDefinedAtLevel))
            .ForMember(dst => dst.AccrualEndPayrollPeriod, opt => opt.MapFrom(src => src.AccrualEndPayrollPeriodDefinedAtLevel))
            .ForMember(dst => dst.PayslipType, opt => opt.MapFrom(src => src.PayslipTypeDefinedAtLevel))
            .ForMember(dst => dst.IsPayoutAtStartOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtStartOfEmploymentDefinedAtLevel))
            .ForMember(dst => dst.IsPayoutAtEndOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtEndOfEmploymentDefinedAtLevel))
            .ForMember(dst => dst.ReservationPayrollComponent, opt => opt.MapFrom(src => src.AdvancePayrollComponentDefinedAtLevel))
            .ForMember(dst => dst.ReservationPercentage, opt => opt.MapFrom(src => src.AdvancePercentageDefinedAtLevel))
            .ForMember(dst => dst.ReservationPayrollPeriod, opt => opt.MapFrom(src => src.AdvancePayrollPeriodNumberDefinedAtLevel))
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.MapFrom(src => src.PeriodicReservationPayrollComponentDefinedAtLevel))
            .ForMember(dst => dst.FinancialReservationPercentage, opt => opt.MapFrom(src => src.FinancialReservationPercentageDefinedAtLevel))
            .ForMember(dst => dst.FinancialMarkupPercentage, opt => opt.MapFrom(src => src.FinancialMarkupPercentageDefinedAtLevel))
            .ForMember(dst => dst.IsCumulativeCalculation, opt => opt.MapFrom(src => src.IsCumulativeCalculationDefinedAtLevel))
            .ForMember(dst => dst.IsPartTimeCalculation, opt => opt.MapFrom(src => src.IsPartTimeCalculationDefinedAtLevel))
            .ForMember(dst => dst.IsAutomaticCalculation, opt => opt.MapFrom(src => src.IsAutomaticCalculationDefinedAtLevel))
            .ForMember(dst => dst.IsSupplementingDailyWage, opt => opt.MapFrom(src => src.IsSupplementingDailyWageDefinedAtLevel))
            .ForMember(dst => dst.MinimumMaximumType, opt => opt.MapFrom(src => src.MinimumMaximumTypeDefinedAtLevel));

        #endregion

        #region POST

        CreateMap<BaseForCalculationPostModel, ModelBaseForCalculation>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items) ? (int)items[nameof(IInheritanceEntity.InheritanceLevelId)] : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.MapFrom(src => src.Key))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.StartPayrollPeriod.PeriodNumber))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.BaseType, opt => opt.MapFrom(src => src.BaseType))
            .ForMember(dst => dst.StartEmployeeAgeType, opt => opt.MapFrom(src => src.StartEmployeeAgeType))
            .ForMember(dst => dst.StartEmployeeAge, opt => opt.MapFrom(src => src.StartEmployeeAge))
            .ForMember(dst => dst.EndEmployeeAgeType, opt => opt.MapFrom(src => src.EndEmployeeAgeType))
            .ForMember(dst => dst.EndEmployeeAge, opt => opt.MapFrom(src => src.EndEmployeeAge))
            .ForMember(dst => dst.ResultPayrollComponentId, opt => opt.MapFrom(src => src.ResultPayrollComponent))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculationPayrollPeriodNumber, opt => opt.MapFrom(src => src.CalculationPayrollPeriod))
            .ForMember(dst => dst.ReferencePayrollPeriodNumber, opt => opt.MapFrom(src => src.ReferencePayrollPeriod))
            .ForMember(dst => dst.PayoutPayrollPeriodNumber, opt => opt.MapFrom(src => src.PayoutPayrollPeriod))
            .ForMember(dst => dst.AccrualEndPayrollPeriodNumber, opt => opt.MapFrom(src => src.AccrualEndPayrollPeriod))
            .ForMember(dst => dst.PayslipType, opt => opt.MapFrom(src => src.PayslipType))
            .ForMember(dst => dst.IsPayoutAtStartOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtStartOfEmployment))
            .ForMember(dst => dst.IsPayoutAtEndOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtEndOfEmployment))
            .ForMember(dst => dst.AdvancePayrollComponentId, opt => opt.MapFrom(src => src.AdvancePayrollComponent))
            .ForMember(dst => dst.AdvancePercentage, opt => opt.MapFrom(src => src.AdvancePercentage))
            .ForMember(dst => dst.AdvancePayrollPeriodNumber, opt => opt.MapFrom(src => src.AdvancePayrollPeriod))
            .ForMember(dst => dst.PeriodicReservationPayrollComponentId, opt => opt.MapFrom(src => src.PeriodicReservationPayrollComponent))
            .ForMember(dst => dst.FinancialReservationPercentage, opt => opt.MapFrom(src => src.FinancialReservationPercentage))
            .ForMember(dst => dst.FinancialMarkupPercentage, opt => opt.MapFrom(src => src.FinancialMarkupPercentage))
            .ForMember(dst => dst.IsCumulativeCalculation, opt => opt.MapFrom(src => src.IsCumulativeCalculation))
            .ForMember(dst => dst.IsPartTimeCalculation, opt => opt.MapFrom(src => src.IsPartTimeCalculation))
            .ForMember(dst => dst.IsAutomaticCalculation, opt => opt.MapFrom(src => src.IsAutomaticCalculation))
            .ForMember(dst => dst.IsSupplementingDailyWage, opt => opt.MapFrom(src => src.IsSupplementingDailyWage))
            .ForMember(dst => dst.MinimumMaximumType, opt => opt.MapFrom(src => src.MinimumMaximumType))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())

            // Navigation properties for audit trail
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseType, opt => opt.Ignore())
            .ForMember(dst => dst.CtStartEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtEndEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtPayslipType, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayoutAtStartOfEmployment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayoutAtEndOfEmployment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsCumulativeCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPartTimeCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsAutomaticCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsSupplementingDailyWage, opt => opt.Ignore())
            .ForMember(dst => dst.CtMinimumMaximumType, opt => opt.Ignore())

            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwAdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwPeriodicReservationPayrollComponent, opt => opt.Ignore());

        // For cloning objects with AutoMapper
        CreateMap<ModelBaseForCalculation, ModelBaseForCalculation>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseType, opt => opt.Ignore())
            .ForMember(dst => dst.CtStartEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtEndEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtPayslipType, opt => opt.Ignore())
            .ForMember(dst => dst.CtMinimumMaximumType, opt => opt.Ignore());
        CreateMap<BaseForCalculationPostModel, BaseForCalculationPostModel>();

        #endregion

        #region PATCH
        
        CreateMap<BaseForCalculationPatchModel, Repository.Entities.BaseForCalculation>()
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.BaseType, opt => opt.MapFrom(src => src.BaseType))
            .ForMember(dst => dst.StartEmployeeAgeType, opt => opt.MapFrom(src => src.StartEmployeeAgeType))
            .ForMember(dst => dst.StartEmployeeAge, opt => opt.MapFrom(src => src.StartEmployeeAge))
            .ForMember(dst => dst.EndEmployeeAgeType, opt => opt.MapFrom(src => src.EndEmployeeAgeType))
            .ForMember(dst => dst.EndEmployeeAge, opt => opt.MapFrom(src => src.EndEmployeeAge))
            .ForMember(dst => dst.ResultPayrollComponentId, opt => opt.MapFrom(src => src.ResultPayrollComponent))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculationPayrollPeriodNumber, opt => opt.MapFrom(src => src.CalculationPayrollPeriod))
            .ForMember(dst => dst.ReferencePayrollPeriodNumber, opt => opt.MapFrom(src => src.ReferencePayrollPeriod))
            .ForMember(dst => dst.PayoutPayrollPeriodNumber, opt => opt.MapFrom(src => src.PayoutPayrollPeriod))
            .ForMember(dst => dst.AccrualEndPayrollPeriodNumber, opt => opt.MapFrom(src => src.AccrualEndPayrollPeriod))
            .ForMember(dst => dst.PayslipType, opt => opt.MapFrom(src => src.PayslipType))
            .ForMember(dst => dst.IsPayoutAtStartOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtStartOfEmployment))
            .ForMember(dst => dst.IsPayoutAtEndOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtEndOfEmployment))
            .ForMember(dst => dst.AdvancePayrollComponentId, opt => opt.MapFrom(src => src.AdvancePayrollComponent))
            .ForMember(dst => dst.AdvancePercentage, opt => opt.MapFrom(src => src.AdvancePercentage))
            .ForMember(dst => dst.AdvancePayrollPeriodNumber, opt => opt.MapFrom(src => src.AdvancePayrollPeriod))
            .ForMember(dst => dst.PeriodicReservationPayrollComponentId, opt => opt.MapFrom(src => src.PeriodicReservationPayrollComponent))
            .ForMember(dst => dst.FinancialReservationPercentage, opt => opt.MapFrom(src => src.FinancialReservationPercentage))
            .ForMember(dst => dst.FinancialMarkupPercentage, opt => opt.MapFrom(src => src.FinancialMarkupPercentage))
            .ForMember(dst => dst.IsCumulativeCalculation, opt => opt.MapFrom(src => src.IsCumulativeCalculation))
            .ForMember(dst => dst.IsPartTimeCalculation, opt => opt.MapFrom(src => src.IsPartTimeCalculation))
            .ForMember(dst => dst.IsAutomaticCalculation, opt => opt.MapFrom(src => src.IsAutomaticCalculation))
            .ForMember(dst => dst.IsSupplementingDailyWage, opt => opt.MapFrom(src => src.IsSupplementingDailyWage))
            .ForMember(dst => dst.MinimumMaximumType, opt => opt.MapFrom(src => src.MinimumMaximumType))
            
            // Ignore the primary key properties as they are not needed for PATCH operations
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())
            
            // Ignore the DefinedAtLevel properties as they are not needed for PATCH operations
            .ForMember(dst => dst.BaseForCalculationIdDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.DescriptionDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.BaseTypeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.StartEmployeeAgeTypeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.StartEmployeeAgeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.EndEmployeeAgeTypeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.EndEmployeeAgeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.ResultPayrollComponentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.CalculationPayrollPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.ReferencePayrollPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PayoutPayrollPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.AccrualEndPayrollPeriodDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PayslipTypeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsPayoutAtStartOfEmploymentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsPayoutAtEndOfEmploymentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollComponentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollPeriodNumberDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.PeriodicReservationPayrollComponentDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FinancialReservationPercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.FinancialMarkupPercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsCumulativeCalculationDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsPartTimeCalculationDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsAutomaticCalculationDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.IsSupplementingDailyWageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.MinimumMaximumTypeDefinedAtLevel, opt => opt.Ignore())
            
            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseType, opt => opt.Ignore())
            .ForMember(dst => dst.CtStartEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtEndEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtPayslipType, opt => opt.Ignore())
            .ForMember(dst => dst.CtMinimumMaximumType, opt => opt.Ignore())
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.Ignore());

        CreateMap<Repository.Entities.BaseForCalculation, ModelBaseForCalculation>()
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.BaseType, opt => opt.MapFrom(src => src.BaseType))
            .ForMember(dst => dst.StartEmployeeAgeType, opt => opt.MapFrom(src => src.StartEmployeeAgeType))
            .ForMember(dst => dst.StartEmployeeAge, opt => opt.MapFrom(src => src.StartEmployeeAge))
            .ForMember(dst => dst.EndEmployeeAgeType, opt => opt.MapFrom(src => src.EndEmployeeAgeType))
            .ForMember(dst => dst.EndEmployeeAge, opt => opt.MapFrom(src => src.EndEmployeeAge))
            .ForMember(dst => dst.ResultPayrollComponentId, opt => opt.MapFrom(src => src.ResultPayrollComponentId))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculationPayrollPeriodNumber, opt => opt.MapFrom(src => src.CalculationPayrollPeriodNumber))
            .ForMember(dst => dst.ReferencePayrollPeriodNumber, opt => opt.MapFrom(src => src.ReferencePayrollPeriodNumber))
            .ForMember(dst => dst.PayoutPayrollPeriodNumber, opt => opt.MapFrom(src => src.PayoutPayrollPeriodNumber))
            .ForMember(dst => dst.AccrualEndPayrollPeriodNumber, opt => opt.MapFrom(src => src.AccrualEndPayrollPeriodNumber))
            .ForMember(dst => dst.PayslipType, opt => opt.MapFrom(src => src.PayslipType))
            .ForMember(dst => dst.IsPayoutAtStartOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtStartOfEmployment))
            .ForMember(dst => dst.IsPayoutAtEndOfEmployment, opt => opt.MapFrom(src => src.IsPayoutAtEndOfEmployment))
            .ForMember(dst => dst.AdvancePayrollComponentId, opt => opt.MapFrom(src => src.AdvancePayrollComponentId))
            .ForMember(dst => dst.AdvancePercentage, opt => opt.MapFrom(src => src.AdvancePercentage))
            .ForMember(dst => dst.AdvancePayrollPeriodNumber, opt => opt.MapFrom(src => src.AdvancePayrollPeriodNumber))
            .ForMember(dst => dst.PeriodicReservationPayrollComponentId, opt => opt.MapFrom(src => src.PeriodicReservationPayrollComponentId))
            .ForMember(dst => dst.FinancialReservationPercentage, opt => opt.MapFrom(src => src.FinancialReservationPercentage))
            .ForMember(dst => dst.FinancialMarkupPercentage, opt => opt.MapFrom(src => src.FinancialMarkupPercentage))
            .ForMember(dst => dst.IsCumulativeCalculation, opt => opt.MapFrom(src => src.IsCumulativeCalculation))
            .ForMember(dst => dst.IsPartTimeCalculation, opt => opt.MapFrom(src => src.IsPartTimeCalculation))
            .ForMember(dst => dst.IsAutomaticCalculation, opt => opt.MapFrom(src => src.IsAutomaticCalculation))
            .ForMember(dst => dst.IsSupplementingDailyWage, opt => opt.MapFrom(src => src.IsSupplementingDailyWage))
            .ForMember(dst => dst.MinimumMaximumType, opt => opt.MapFrom(src => src.MinimumMaximumType))

            // Mapping the primary key properties
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.BaseForCalculationId, opt => opt.MapFrom(src => src.BaseForCalculationId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))

            // Ignore the navigation properties as they are not needed for PATCH operations
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.ResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwResultPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.AdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwAdvancePayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.PeriodicReservationPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.VwPeriodicReservationPayrollComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtBaseType, opt => opt.Ignore())
            .ForMember(dst => dst.CtStartEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtEndEmployeeAgeType, opt => opt.Ignore())
            .ForMember(dst => dst.CtPayslipType, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayoutAtStartOfEmployment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPayoutAtEndOfEmployment, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsCumulativeCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsPartTimeCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsAutomaticCalculation, opt => opt.Ignore())
            .ForMember(dst => dst.CtIsSupplementingDailyWage, opt => opt.Ignore())
            .ForMember(dst => dst.CtMinimumMaximumType, opt => opt.Ignore());

        #endregion
    }
}