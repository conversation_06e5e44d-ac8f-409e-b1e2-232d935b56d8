using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Post;
using Vsp.PayrollConfiguration.Infrastructure.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Models;

public class BaseForCalculationPostModel : BaseForCalculationPatchModel, IArrangementPostModel
{
    [JsonIgnore]
    public Guid InheritanceLevelId { get; set; }

    [Required]
    [Range(1900, 9999)]
    public int? Year { get; set; }

    [Required]
    [Range(1, 25)]
    public int? Key { get; set; }

    [Required]
    public PayrollPeriodNumberModel StartPayrollPeriod { get; set; } = null!;
}