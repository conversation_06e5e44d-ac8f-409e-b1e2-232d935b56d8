using Vsp.PayrollConfiguration.Repository.Interfaces;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Enums;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculation.Validators;

internal class DeleteBaseForCalculationValidator : AbstractValidator<ModelBaseForCalculation>
{
    private readonly ILoketContext loketContext;

    public DeleteBaseForCalculationValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;

        Include(new DeleteInheritanceEntityValidator<ModelBaseForCalculation>(this.loketContext, mapper));

        WhenAsync((entity, token) => DeleteInheritanceEntityValidator<ModelBaseForCalculation>.EntityIsDefinedOnCurrentInheritanceLevelAsync(entity, loketContext, mapper, token), () =>
        {
            // Rule 1: Base for calculation still has configuration: base payroll component
            RuleFor(x => x)
                .MustAsync(async (entity, token) => !await HasBasePayrollComponentsAsync(entity, token))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_BasePayrollComponent)
                .WithMessage("Base for calculation still has configuration: base payroll component");

            // Rule 2: Base for calculation still has configuration: age-based minimum
            RuleFor(x => x)
                .MustAsync(async (entity, token) => !await HasAgeBasedMinimumAsync(entity, token))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMinimum)
                .WithMessage("Base for calculation still has configuration: age-based minimum");

            // Rule 3: Base for calculation still has configuration: age-based maximum
            RuleFor(x => x)
                .MustAsync(async (entity, token) => !await HasAgeBasedMaximumAsync(entity, token))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_AgeBasedMaximum)
                .WithMessage("Base for calculation still has configuration: age-based maximum");

            // Rule 4: Base for calculation is used in: employment profile
            RuleFor(x => x)
                .MustAsync(async (entity, token) => !await IsUsedInEmploymentProfileAsync(entity, token))
                .WithErrorCode(MessageCodes.API_PayrollConfiguration_BaseForCalculation_Delete_InUse_EmploymentProfile)
                .WithMessage("Base for calculation is used in: employment profile");
        });
    }

    private async Task<bool> HasBasePayrollComponentsAsync(ModelBaseForCalculation entity, CancellationToken ct) =>
        await this.loketContext.Set<ModelBaseForCalculationBasePayrollComponent>()
            .AnyAsync(x =>
                    x.InheritanceLevelId == entity.InheritanceLevelId &&
                    x.YearId == entity.YearId &&
                    x.BaseForCalculationId == entity.BaseForCalculationId, ct);

    private async Task<bool> HasAgeBasedMinimumAsync(ModelBaseForCalculation entity, CancellationToken ct) =>
        await this.loketContext.Set<ModelBaseForCalculationAgeBasedMinimum>()
            .AnyAsync(x =>
                    x.InheritanceLevelId == entity.InheritanceLevelId &&
                    x.YearId == entity.YearId &&
                    x.BaseForCalculationId == entity.BaseForCalculationId, ct);

    private async Task<bool> HasAgeBasedMaximumAsync(ModelBaseForCalculation entity, CancellationToken ct) =>
        await this.loketContext.Set<ModelBaseForCalculationAgeBasedMaximum>()
            .AnyAsync(x =>
                    x.InheritanceLevelId == entity.InheritanceLevelId &&
                    x.YearId == entity.YearId &&
                    x.BaseForCalculationId == entity.BaseForCalculationId, ct);

    private async Task<bool> IsUsedInEmploymentProfileAsync(ModelBaseForCalculation entity, CancellationToken ct) =>
        await this.loketContext.Set<ModelEmployeeProfile>()
            .AnyAsync(x =>
                entity.PayrollPeriodId == 1 &&
                x.InheritanceLevelId == entity.InheritanceLevelId &&
                x.YearId == entity.YearId &&
                x.ModelEmployeeProfileArrangements.Any(x => x.ArrangementType == (int)ArrangementType.BaseForCalculation && x.Identification == entity.BaseForCalculationId), ct);
}