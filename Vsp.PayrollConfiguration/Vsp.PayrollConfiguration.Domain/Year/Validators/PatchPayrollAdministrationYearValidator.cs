using Vsp.PayrollConfiguration.Domain.Shared.Extensions;
using Vsp.PayrollConfiguration.Domain.Year.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Infrastructure.Extensions;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Year.Validators;

/// <summary>
/// Validator for the YearPatchModel.
/// </summary>
internal class PatchPayrollAdministrationYearValidator : InitializableAbstractValidator<YearPaPatchModel, PatchPayrollAdministrationYearValidationInfo>
{
    private readonly ILoketContext loketContext;
    private readonly IDateTimeProvider dateTimeProvider;

    public PatchPayrollAdministrationYearValidator(ILoketContext loketContext, IDateTimeProvider dateTimeProvider)
    {
        this.loketContext = loketContext;
        this.dateTimeProvider = dateTimeProvider;

        // Rule: testYear must be false if the previous year has testYear = false
        RuleFor(x => x)
            .MustAsync(BeValidTestYearBasedOnPreviousYearAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_TestYear_PreviousYearNotTest)
            .WithMessage("testYear must be false if the previous year has testYear false.");

        // Rule: testYear must be false if a payroll tax return message has been sent this year
        RuleFor(x => x)
            .MustAsync(BeValidTestYearBasedOnPayrollTaxReturnAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_TestYear_PayrollTaxReturnPerformed)
            .WithMessage("testYear must be false if a payroll tax return message has been sent this year.");

        // Rule: zwSelfInsurerStartPayrollPeriod is invalid
        RuleFor(x => x)
            .MustAsync(BeValidZwSelfInsurerStartPayrollPeriodAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_ZwSelfInsurerStartPayrollPeriod_Invalid)
            .WithMessage("zwSelfInsurerStartPayrollPeriod is invalid.");

        // Rule: aof.key is invalid
        RuleFor(x => x)
            .MustAsync(BeValidAofKeyAsync)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_Aof_Invalid)
            .WithMessage("aof.key is invalid.");

        // Rule: aof must be unset for year < 2022
        RuleFor(x => x)
            .Must(BeValidAofBasedOnYear)
            .When(x => ParseYearKeysFromId(x).YearId < 2022)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_Aof_MustNotHaveValueBefore2022)
            .WithMessage("aof must be unset for year < 2022.");

        // Rule: aof must be set for year >= 2022
        RuleFor(x => x)
            .Must(BeValidAofBasedOnYear)
            .When(x => ParseYearKeysFromId(x).YearId >= 2022)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_Aof_MustHaveValueFrom2022)
            .WithMessage("aof must be set for year >= 2022.");

        // Rule: current dateAvailableEss must be null or in the future.
        RuleFor(x => x)
            .Must(DateAvailableEssCurrentValueIsNullOrInTheFuture)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_DateAvailableEss_OldDateAvailableEssNotEmptyAndNotInFuture)
            .WithMessage("dateAvailableEss may only be changed when the current value is empty or in the future.");

        // Rule: dateAvailableEss must be set to a future date if not left empty.
        RuleFor(x => x)
            .Must(HaveDateAvailableEssSentValueNullOrInTheFuture)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssNotEmptyAndNotInFuture)
            .WithMessage("dateAvailableEss must be set to a future date if not left empty.");

        // Rule: current dateAvailableEss may only be changed when a request for year closure has been sent, or the year has been closed.
        RuleFor(x => x)
            .Must(HaveDateAvailableEssDifferentToCurrentValueOnlyForYearClosedOrWithClosureRequested)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_DateAvailableEss_YearClosureNotRequestedAndYearNotClosed)
            .WithMessage("dateAvailableEss may only be changed when a request for year closure has been sent, or the year has been closed.");

        // Rule: dateAvailableEss may only be cleared when the year has not been closed yet.
        RuleFor(x => x)
            .Must(HaveDateAvailableEssNullOnlyWhenTheYearHasNotBeenClosedYet)
            .When(x => x.DateAvailableEss == null)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_DateAvailableEss_NewDateAvailableEssEmptyAndYearClosed)
            .WithMessage("dateAvailableEss may only be cleared when the year has not been closed yet.");

        // Rule: current sendEssMail may only be changed when a request for year closure has been sent, or the year has been closed.
        RuleFor(x => x)
            .Must(HaveNewSendEssMailValueOnlyWhenYearClosureIsRequestedOrPerformed)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_SendEssMail_YearClosureNotRequestedAndYearNotClosed)
            .WithMessage("sendEssMail may only be changed when a request for year closure has been sent, or the year has been closed.");

        // Rule: current sendEssMail may only be changed when dateAvailableEss is null or in the future.
        RuleFor(x => x)
            .Must(HaveNewSendEssMailValueOnlyIfDateAvailableEssCurrentValueIsNullOrInTheFuture)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_Year_SendEssMail_OldDateAvailableEssNotEmptyAndNotInFuture)
            .WithMessage("sendEssMail may only be changed when the current dateAvailableEss is empty or in the future.");
    }

    private static Repository.Entities.Year ParseYearKeysFromId(YearPaPatchModel patchModel)
    {
        var yearEntity = new Repository.Entities.Year { Id = patchModel.Id };
        GeneratedIdHelper.GenerateIdKeys(yearEntity);
        return yearEntity;
    }

    private async Task<bool> BeValidTestYearBasedOnPreviousYearAsync(YearPaPatchModel patchModel, CancellationToken token)
    {
        if (!patchModel.TestYear.HasValue) return false;
        if (!patchModel.TestYear.Value) return true;

        var yearEntity = ParseYearKeysFromId(patchModel);
        yearEntity.YearId--;

        var previousYear = await this.loketContext.Set<Repository.Entities.Year>()
            .Where(y => y.YearId == yearEntity.YearId && y.InheritanceLevelId == yearEntity.InheritanceLevelId)
            .FirstOrDefaultAsync(token);

        return previousYear is null || previousYear.TestYear == (int)YesNo.Yes;
    }

    private async Task<bool> BeValidTestYearBasedOnPayrollTaxReturnAsync(YearPaPatchModel patchModel, CancellationToken token)
    {
        if (!patchModel.TestYear.HasValue) return false;
        if (!patchModel.TestYear.Value) return true;

        var yearEntity = this.loketContext.Set<Repository.Entities.Year>()
            .Include(x => x.InheritanceLevel)
            .ThenInclude(x => x.Administration)
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year>(patchModel.Id)).Single();

        var payrollTaxReturnMessage = await this.loketContext.Set<PayrollTaxReturnMessage>()
            .Where(x => x.YearId == yearEntity.YearId && x.WageTaxNumber == yearEntity.InheritanceLevel.Administration!.WageTaxNumber)
            .FirstOrDefaultAsync(token);

        return payrollTaxReturnMessage is null;
    }


    private async Task<bool> BeValidZwSelfInsurerStartPayrollPeriodAsync(YearPaPatchModel patchModel, CancellationToken token)
    {
        if (patchModel.ZwSelfInsurerStartPayrollPeriod?.PeriodNumber is null) return true;

        var payrollPeriod = await this.loketContext.Set<PayrollPeriod>()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, PayrollPeriod>(patchModel.Id, x => x.Year))
            .Where(x => x.PayrollPeriodId == patchModel.ZwSelfInsurerStartPayrollPeriod.PeriodNumber)
            .SingleOrDefaultAsync(token);

        return payrollPeriod is not null;
    }

    private async Task<bool> BeValidAofKeyAsync(YearPaPatchModel patchModel, CancellationToken token)
    {
        var aofKey = patchModel.Aof?.Key ?? 0;

        return await this.loketContext.Set<CtAof>().AnyAsync(x => x.Code == aofKey, cancellationToken: token);
    }

    private bool BeValidAofBasedOnYear(YearPaPatchModel patchModel)
    {
        var yearEntity = ParseYearKeysFromId(patchModel);
        var isYear2022OrLater = yearEntity.YearId >= 2022;
        var hasValidAofValue = patchModel.Aof?.Key is not null and not 0;

        return isYear2022OrLater ? hasValidAofValue : !hasValidAofValue;
    }

    private bool DateAvailableEssCurrentValueIsNullOrInTheFuture(
        YearPaPatchModel patchModel)
    {
        var oldYear = this.ValidationInformation.OldYear;
        var oldDateAvailableEss = oldYear.DateAvailableEss;
        return OldDateAvailableEssValueIsIsKept(patchModel, oldYear) ||
               oldDateAvailableEss is null || oldDateAvailableEss > this.dateTimeProvider.DateOnlyToday();
    }

    private bool HaveDateAvailableEssSentValueNullOrInTheFuture(
        YearPaPatchModel patchModel)
    {
        var oldYear = this.ValidationInformation.OldYear;
        var newDateAvailableEss = patchModel.DateAvailableEss;
        return OldDateAvailableEssValueIsIsKept(patchModel, oldYear) ||
               newDateAvailableEss is null || newDateAvailableEss.Value > this.dateTimeProvider.DateOnlyToday();
    }

    private bool HaveDateAvailableEssDifferentToCurrentValueOnlyForYearClosedOrWithClosureRequested(
        YearPaPatchModel patchModel)
    {
        var oldYear = this.ValidationInformation.OldYear;
        return OldDateAvailableEssValueIsIsKept(patchModel, oldYear) ||
               IsYearClosureRequestedOrPerformed(oldYear);
    }

    private bool HaveDateAvailableEssNullOnlyWhenTheYearHasNotBeenClosedYet(
        YearPaPatchModel patchModel)
        => this.ValidationInformation.OldYear.YearTransitionIsPerformed != (int)YesNo.Yes;

    private bool HaveNewSendEssMailValueOnlyWhenYearClosureIsRequestedOrPerformed(
        YearPaPatchModel patchModel)
    {
        var oldYear = this.ValidationInformation.OldYear;
        return SendEssMailCurrentValueIsKept(patchModel, oldYear) ||
               IsYearClosureRequestedOrPerformed(oldYear);
    }

    private bool HaveNewSendEssMailValueOnlyIfDateAvailableEssCurrentValueIsNullOrInTheFuture(YearPaPatchModel patchModel)
    {
        var oldYear = this.ValidationInformation.OldYear;
        return SendEssMailCurrentValueIsKept(patchModel, oldYear) ||
               oldYear.DateAvailableEss is null || oldYear.DateAvailableEss > this.dateTimeProvider.DateOnlyToday();
    }

    private bool OldDateAvailableEssValueIsIsKept(YearPaPatchModel patchModel, Repository.Entities.Year year)
        => patchModel.DateAvailableEss == year.DateAvailableEss;

    private static bool SendEssMailCurrentValueIsKept(YearPaPatchModel patchModel, Repository.Entities.Year year)
        => patchModel.SendEssMail!.Value.ToYesNoInt() == year.SendEssMail;

    private bool IsYearClosureRequestedOrPerformed(Repository.Entities.Year currentYear)
    {
        const int yes = (int)YesNo.Yes;
        return currentYear.YearTransitionIsRequested == yes || currentYear.YearTransitionIsPerformed == yes;
    }

    protected override async Task<PatchPayrollAdministrationYearValidationInfo> Initialize(YearPaPatchModel obj) =>
        new PatchPayrollAdministrationYearValidationInfo()
        {
            OldYear = await this.loketContext.Set<Repository.Entities.Year>()
                .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year>(obj.Id))
                .SingleAsync()
        };
}

public class PatchPayrollAdministrationYearValidationInfo
{
    public Repository.Entities.Year OldYear { get; set; } = null!;
}