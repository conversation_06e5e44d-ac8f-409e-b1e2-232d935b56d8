using Vsp.PayrollConfiguration.Domain.Shared.Models;

namespace Vsp.PayrollConfiguration.Domain.Year.Models;

public class YearPaModel : YearClaWmModel
{
    public bool TestYear { get; set; }
    public YearTransition YearTransition { get; set; } = null!;

    public DateOnly? DateAvailableEss { get; set; }
    public bool SendEssMail { get; set; }

    public DateOnly? DateEssMail { get; set; }
    public PayrollPeriodModel ZwSelfInsurerStartPayrollPeriod { get; set; } = null!;
    public KeyValueModel? Aof { get; set; }
}

public class YearTransition
{
    public bool IsRequested { get; set; }
    public bool IsPerformed { get; set; }

    public DateOnly? PerformedDate { get; set; }
}