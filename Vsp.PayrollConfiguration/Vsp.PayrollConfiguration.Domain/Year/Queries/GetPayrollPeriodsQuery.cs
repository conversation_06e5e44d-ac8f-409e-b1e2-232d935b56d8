using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.Year.Queries;

internal class GetPayrollPeriodsQuery(IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<PayrollPeriodModel, PayrollPeriodPayrollTaxReturn, Repository.Entities.Year, ILoketContext>(dependencies)
{
    public override Expression<Func<PayrollPeriodPayrollTaxReturn, bool>>? FilterCollectionByExpression(Guid id)
    {
        var year = this.context.Years
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year>(id))
            .Single();

        return pp => pp.PayrollPeriodType == year.PayrollPeriodType && pp.YearId == year.YearId;
    }
}