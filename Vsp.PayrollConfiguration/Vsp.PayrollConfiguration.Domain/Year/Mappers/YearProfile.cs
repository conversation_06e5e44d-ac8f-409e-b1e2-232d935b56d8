using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.Year.Models;

namespace Vsp.PayrollConfiguration.Domain.Year.Mappers;

internal class YearProfile : Profile
{
    public YearProfile()
    {
        // GET
        CreateMap<Repository.Entities.Year, YearClaWmModel>()
            .IncludeBase<Repository.Entities.Year, YearMinimizedModel>()
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.StandardShiftNumber, opt => opt.MapFrom(src => src.StandardShiftNumber))
            .ForMember(dst => dst.StandardShift, opt => opt.Ignore())
            .ForMember(dst => dst.StandardEmployeeProfile, opt => opt.MapFrom(
                src => src.StandardEmployeeProfileNumber == 0
                    ? null
                    : new EmployeeProfile()
                    {
                        EmployeeProfileId = src.StandardEmployeeProfileNumber,
                        Omschrijving = (src.StandardEmployeeProfile == null ? null : src.StandardEmployeeProfile.Omschrijving)!
                    }))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.Year, YearPaModel>()
            .IncludeBase<Repository.Entities.Year, YearClaWmModel>()
            .ForMember(dst => dst.TestYear, opt => opt.MapFrom(src => src.TestYear))
            .ForMember(dst => dst.YearTransition, opt => opt.MapFrom(src => src))
            .ForMember(dst => dst.DateAvailableEss, opt => opt.MapFrom(src => src.DateAvailableEss))
            .ForMember(dst => dst.SendEssMail, opt => opt.MapFrom(src => src.SendEssMail))
            .ForMember(dst => dst.DateEssMail, opt => opt.MapFrom(src => src.DateEssMail))
            .ForMember(dst => dst.ZwSelfInsurerStartPayrollPeriod, opt => opt.MapFrom(src => src.ZwSelfInsurerStartPayrollPeriod))
            .ForMember(dst => dst.Aof, opt => opt.MapFrom(src => src.CtAof));

        CreateMap<Repository.Entities.Shift, StandardShiftModel>()
            .ForMember(dst => dst.ShiftNumber, opt => opt.MapFrom(src => src.ShiftId))
            .ForMember(dst => dst.FullTimeHoursPerWeek, opt => opt.MapFrom(src => src.FullTimeHoursPerWeek))
            .ForMember(dst => dst.BonusPercentage, opt => opt.MapFrom(src => src.BonusPercentage))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId));

        CreateMap<EmployeeProfile, StandardEmployeeProfileModel>()
            .ForMember(dst => dst.EmployeeProfileNumber, opt => opt.MapFrom(src => src.EmployeeProfileId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Omschrijving));

        CreateMap<Repository.Entities.Year, YearTransition>()
            .ForMember(dst => dst.IsRequested, opt => opt.MapFrom(src => src.YearTransitionIsRequested))
            .ForMember(dst => dst.IsPerformed, opt => opt.MapFrom(src => src.YearTransitionIsPerformed))
            .ForMember(dst => dst.PerformedDate, opt => opt.MapFrom(src => src.YearTransitionPerformedDate));

        CreateMap<Repository.Entities.Year, YearDefinedAtLevelModel>()
            .ForMember(dst => dst.PayrollPeriodType, opt => opt.MapFrom(src => src.PayrollPeriodTypeDefinedAtLevel))
            .ForMember(dst => dst.StandardShift, opt => opt.MapFrom(src => src.StandardShiftDefinedAtLevel))
            .ForMember(dst => dst.StandardEmployeeProfile, opt => opt.MapFrom(src => src.StandardEmployeeProfileDefinedAtLevel));

        // PATCH
        CreateMap<YearWmPatchModel, Repository.Entities.Year>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodType, opt => opt.Ignore())
            .ForMember(dst => dst.StandardShiftNumber, opt => opt.MapFrom(src =>
                src.StandardShift == null ? 0 : src.StandardShift.ShiftNumber))
            .ForMember(dst => dst.StandardEmployeeProfileNumber, opt => opt.MapFrom(src =>
                src.StandardEmployeeProfile == null ? 0 : src.StandardEmployeeProfile.EmployeeProfileNumber))
            .ForMember(dst => dst.TestYear, opt => opt.Ignore())
            .ForMember(dst => dst.YearTransitionIsRequested, opt => opt.Ignore())
            .ForMember(dst => dst.YearTransitionIsPerformed, opt => opt.Ignore())
            .ForMember(dst => dst.YearTransitionPerformedDate, opt => opt.Ignore())
            .ForMember(dst => dst.DateAvailableEss, opt => opt.Ignore())
            .ForMember(dst => dst.SendEssMail, opt => opt.Ignore())
            .ForMember(dst => dst.DateEssMail, opt => opt.Ignore())
            .ForMember(dst => dst.ZwSelfInsurerStartPayrollPeriodNumber, opt => opt.Ignore())
            .ForMember(dst => dst.Aof, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodTypeDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.StandardShiftDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.StandardEmployeeProfileDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.CtPayrollPeriodType, opt => opt.Ignore())
            .ForMember(dst => dst.CtTestYear, opt => opt.Ignore())
            .ForMember(dst => dst.CtYearTransitionIsRequested, opt => opt.Ignore())
            .ForMember(dst => dst.CtYearTransitionIsPerformed, opt => opt.Ignore())
            .ForMember(dst => dst.CtSendEssMail, opt => opt.Ignore())
            .ForMember(dst => dst.CtAof, opt => opt.Ignore())
            .ForMember(dst => dst.ZwSelfInsurerStartPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwStandardShift, opt => opt.Ignore())
            .ForMember(dst => dst.StandardEmployeeProfile, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriods, opt => opt.Ignore());
    }
}