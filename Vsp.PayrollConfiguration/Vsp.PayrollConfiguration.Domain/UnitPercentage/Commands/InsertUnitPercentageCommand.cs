using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Commands;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Commands;

internal class InsertUnitPercentageCommand(
    IInsertCommandDependencies<ILoketContext> dependencies,
    IGetInheritanceEntityQuery<UnitPercentageModel, Repository.Entities.UnitPercentage> query,
    IServiceProvider serviceProvider)
    : InsertInheritanceEntityCommand<UnitPercentagePostModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage>(dependencies, query, serviceProvider)
{
    protected override bool AddFutureYears => true;
}