using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Interfaces;
using Vsp.PayrollConfiguration.Repository.Enums;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using Z.EntityFramework.Plus;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Helpers;

public class UnitPercentageMetadataHelper(ILoketContext loketContext, IMapper mapper)
    : IUnitPercentageMetadataHelper
{
    private readonly ILoketContext loketContext = loketContext;
    private readonly IMapper mapper = mapper;

    public QueryFutureEnumerable<PayrollComponentMinimizedModel> GetComponentsFuture(Guid yearId) =>
        this.loketContext.Set<Component>()
            .Where(GeneratedIdHelper.ConstructWhere<Repository.Entities.Year, Component>(yearId, x => x.Year))
            .Where(c => c.Category == (int)Category.Units)
            .OrderBy(c => c.ComponentId)
            .ProjectTo<PayrollComponentMinimizedModel>(this.mapper.ConfigurationProvider)
            .Future();
}
