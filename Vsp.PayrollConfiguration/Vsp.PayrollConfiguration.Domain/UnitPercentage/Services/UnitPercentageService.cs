using Vsp.PayrollConfiguration.Domain.Shared.Interfaces;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Interfaces;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Services;

internal class UnitPercentageService(
    IFilteredQuery<UnitPercentageModel, Repository.Entities.UnitPercentage, Repository.Entities.Year, ILoketContext> query,
    IUnitPercentageMetadataHelper metadataHelper,
    ICodeTableHelper codeTableHelper,
    IInsertInheritanceEntityCommand<UnitPercentagePostModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage> insertCommand,
    IPatchInheritanceEntityCommand<UnitPercentagePatchModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage> patchCommand,
    IDeleteInheritanceEntityCommand<ModelUnitPercentage> deleteCommand)
    : IUnitPercentageService
{
    private readonly IFilteredQuery<UnitPercentageModel, Repository.Entities.UnitPercentage, Repository.Entities.Year, ILoketContext> query = query;
    private readonly IUnitPercentageMetadataHelper metadataHelper = metadataHelper;
    private readonly ICodeTableHelper codeTableHelper = codeTableHelper;
    private readonly IInsertInheritanceEntityCommand<UnitPercentagePostModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage> insertCommand = insertCommand;
    private readonly IPatchInheritanceEntityCommand<UnitPercentagePatchModel, UnitPercentageModel, Repository.Entities.UnitPercentage, ModelUnitPercentage> patchCommand = patchCommand;
    private readonly IDeleteInheritanceEntityCommand<ModelUnitPercentage> deleteCommand = deleteCommand;

    public async Task<IListOperationResult<UnitPercentageModel>> GetUnitPercentagesByYearIdAsync(Guid yearId) =>
        await this.query.ExecuteList(yearId);

    public async Task<IOperationResult<UnitPercentageModel>> PostUnitPercentageByInheritanceLevelIdAsync(Guid inheritanceLevelId, UnitPercentagePostModel postModel)
    {
        postModel.InheritanceLevelId = inheritanceLevelId;
        return await this.insertCommand.ExecuteAsync(postModel);
    }

    public async Task<IOperationResult<UnitPercentageModel>> PatchUnitPercentageByUnitPercentageIdAsync(Guid unitPercentageId, UnitPercentagePatchModel patchModel)
    {
        patchModel.Id = unitPercentageId;
        return await this.patchCommand.ExecuteAsync(patchModel);
    }

    public async Task<IOperationResult<NoResult>> DeleteUnitPercentageByUnitPercentageIdAsync(Guid unitPercentageId) =>
        await this.deleteCommand.ExecuteAsync(unitPercentageId);

    public async Task<IOperationResult<UnitPercentageMetadataModel>> GetUnitPercentageMetadataByYearIdAsync(Guid yearId)
    {
        var components = this.metadataHelper.GetComponentsFuture(yearId);
        var calculateOvers = await this.codeTableHelper.GetOptions<CtCalculateOver>();

        var model = new UnitPercentageMetadataModel()
        {
            PayrollComponent = await components.ToListAsync(),
            CalculateOver = calculateOvers
        };

        return new OperationResult<UnitPercentageMetadataModel>(model);
    }
}
