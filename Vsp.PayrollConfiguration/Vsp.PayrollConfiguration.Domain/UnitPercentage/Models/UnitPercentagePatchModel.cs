using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;

public class UnitPercentagePatchModel : UnitPercentageBaseModel, IPatchModel
{
    [JsonIgnore]
    public Guid Id { get; set; }

    /// <summary>
    /// The calculation method.
    /// </summary>
    [Required]
    public KeyModel CalculateOver { get; set; } = null!;
}