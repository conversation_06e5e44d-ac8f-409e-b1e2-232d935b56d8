using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Mappers;

internal class UnitPercentageProfile : Profile
{
    public UnitPercentageProfile()
    {
        #region GET

        CreateMap<Repository.Entities.UnitPercentage, UnitPercentageModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.PayrollComponent, opt => opt.MapFrom(src => src.Component))
            .ForMember(dst => dst.StartPayrollPeriod, opt => opt.MapFrom(src => src.PayrollPeriod))
            .ForMember(dst => dst.DefinedAtLevel, opt => opt.MapFrom(src => src))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculateOver, opt => opt.MapFrom(src => src.CtCalculateOver))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId));

        CreateMap<Repository.Entities.UnitPercentage, UnitPercentageDefinedAtLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.ComponentIdDefinedAtLevel))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.PercentageDefinedAtLevel))
            .ForMember(dst => dst.CalculateOver, opt => opt.MapFrom(src => src.CalculateOverDefinedAtLevel));

        #endregion

        #region POST

        CreateMap<UnitPercentagePostModel, ModelUnitPercentage>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId,
                opt => opt.MapFrom((_, _, _, context) => context.TryGetItems(out var items)
                    ? (int)items[nameof(IInheritanceEntity.InheritanceLevelId)]
                    : default))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.Year))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.PayrollComponent.Key))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.StartPayrollPeriod.PeriodNumber))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculateOver, opt => opt.MapFrom(src => src.CalculateOver.Key))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtCalculateOver, opt => opt.Ignore());

        #endregion

        #region PATCH

        CreateMap<UnitPercentagePatchModel, Repository.Entities.UnitPercentage>()
            .ForMember(dst => dst.Id, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.Ignore())
            .ForMember(dst => dst.YearId, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentId, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.Ignore())
            .ForMember(dst => dst.ComponentIdDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.PercentageDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.CalculateOver, opt => opt.MapFrom(src => src.CalculateOver.Key))
            .ForMember(dst => dst.CalculateOverDefinedAtLevel, opt => opt.Ignore())
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.Year, opt => opt.Ignore())
            .ForMember(dst => dst.Component, opt => opt.Ignore())
            .ForMember(dst => dst.ModelComponent, opt => opt.Ignore())
            .ForMember(dst => dst.PayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.CtCalculateOver, opt => opt.Ignore());
        CreateMap<Repository.Entities.UnitPercentage, ModelUnitPercentage>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevelId, opt => opt.MapFrom(src => src.InheritanceLevelId))
            .ForMember(dst => dst.YearId, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.ComponentId, opt => opt.MapFrom(src => src.ComponentId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.PayrollPeriodId))
            .ForMember(dst => dst.Percentage, opt => opt.MapFrom(src => src.Percentage))
            .ForMember(dst => dst.CalculateOver, opt => opt.MapFrom(src => src.CalculateOver))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtCalculateOver, opt => opt.Ignore());

        #endregion

        // For cloning objects with AutoMapper
        CreateMap<ModelUnitPercentage, ModelUnitPercentage>()
            .ForMember(dst => dst.InheritanceLevel, opt => opt.Ignore())
            .ForMember(dst => dst.VwPayrollPeriod, opt => opt.Ignore())
            .ForMember(dst => dst.VwComponent, opt => opt.Ignore())
            .ForMember(dst => dst.CtCalculateOver, opt => opt.Ignore());
        CreateMap<UnitPercentagePostModel, UnitPercentagePostModel>();
    }
}