using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Domain.UnitPercentage.Models;
using Vsp.PayrollConfiguration.Infrastructure.Constants;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Validators;

/// <summary>
/// Validator for the UnitPercentagePatchModel.
/// </summary>
internal class PatchUnitPercentageValidator : AbstractValidator<UnitPercentagePatchModel>
{
    private readonly ILoketContext loketContext;
    private readonly IMapper mapper;

    /// <summary>
    /// Initializes a new instance of the <see cref="PatchUnitPercentageValidator"/> class.
    /// </summary>
    /// <param name="loketContext">The context for accessing the database.</param>
    /// <param name="mapper">The mapper for object-object mapping.</param>
    public PatchUnitPercentageValidator(ILoketContext loketContext, IMapper mapper)
    {
        this.loketContext = loketContext;
        this.mapper = mapper;

        // Rule to check if the CalculateOver property is valid.
        RuleFor(x => x.CalculateOver)
            .MustAsync(BeValidCalculateOverCode)
            .WithErrorCode(MessageCodes.API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid)
            .WithMessage("calculateOver.key is invalid.");
    }

    /// <summary>
    /// Asynchronous validation method to check if the CalculateOver code exists in the code table.
    /// </summary>
    /// <param name="calculateOver">The CalculateOver key model.</param>
    /// <param name="token">The cancellation token.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the code is valid.</returns>
    private async Task<bool> BeValidCalculateOverCode(KeyModel calculateOver, CancellationToken token) =>
        await this.loketContext.Set<CtCalculateOver>().AnyAsync(c => c.Code == calculateOver.Key, token);
}