using Vsp.PayrollConfiguration.Infrastructure.Validators;
using Vsp.PayrollConfiguration.Repository.Interfaces;

namespace Vsp.PayrollConfiguration.Domain.UnitPercentage.Validators;

internal class DeleteUnitPercentageValidator : AbstractValidator<ModelUnitPercentage>
{
    public DeleteUnitPercentageValidator(ILoketContext loketContext, IMapper mapper) =>
        Include(new DeleteInheritanceEntityValidator<ModelUnitPercentage>(loketContext, mapper));
}