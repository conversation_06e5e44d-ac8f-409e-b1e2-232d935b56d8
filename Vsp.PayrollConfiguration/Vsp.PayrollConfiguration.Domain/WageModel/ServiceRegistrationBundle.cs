using Vsp.PayrollConfiguration.Domain.WageModel.Authorizations;
using Vsp.PayrollConfiguration.Domain.WageModel.Interfaces;
using Vsp.PayrollConfiguration.Domain.WageModel.Models;
using Vsp.PayrollConfiguration.Domain.WageModel.Queries;
using Vsp.PayrollConfiguration.Domain.WageModel.Services;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.WageModel;

public class ServiceRegistrationBundle : IServiceRegistrationBundle
{
    public void RegisterBundle(IServiceCollection services)
    {
        services.AddScoped<IAuthorizeApiProtocol<WageModelAuthorizationModel>, WageModelAuthorization>();
        services.AddScoped<IWageModelService, WageModelService>();
        services.AddScoped<IFilteredQuery<WageModelModel, InheritanceLevel, InheritanceLevel, ILoketContext>, GetWageModelsQuery>();
        services.AddScoped<IGetByIdQuery<WageModelModel, InheritanceLevel, ILoketContext>, GetWageModelByIdQuery>();
    }
}
