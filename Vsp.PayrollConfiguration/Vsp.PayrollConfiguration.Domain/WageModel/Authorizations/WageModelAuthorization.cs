using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.WageModel.Authorizations;

public class WageModelAuthorizationModel
{
    public Guid WageModelId { get; set; }
}

public class WageModelAuthorization : AuthorizeLoketBase<WageModelAuthorizationModel>
{
    public override Task<(ResourceType ResourceType, Guid EntityId)> AuthorizeLoketEntity(ICurrentContext currentContext, WageModelAuthorizationModel authorizationObject) =>
        Task.FromResult((ResourceType.LoonModel, authorizationObject.WageModelId));

    public override Task<InheritanceLevel?> AuthorizeLoketInheritanceLevel(ICurrentContext currentContext, WageModelAuthorizationModel authorizationObject) =>
        Task.FromResult((InheritanceLevel?)InheritanceLevel.WageModel);
}
