using Vsp.PayrollConfiguration.Domain.CollectiveLaborAgreement.Models;

namespace Vsp.PayrollConfiguration.Domain.WageModel.Models;

/// <summary>
/// Details of a wage model.
/// </summary>
public class WageModelModel
{
    /// <summary>
    /// The unique identifier of a wage model (GUID/UUID).
    /// </summary>
    /// <example>123e4567-e89b-12d3-a456-************</example>
    public Guid Id { get; set; }

    /// <summary>
    /// Name of the wage model.
    /// </summary>
    /// <example>Loonmodel bloemendetailhandel</example>
    [JsonProperty(Required = Required.DisallowNull)]
    public string Description { get; set; } = null!;

    /// <summary>
    /// Additional comment.
    /// </summary>
    /// <example>No comments</example>
    public string? Comment { get; set; }

    public CollectiveLaborAgreementModel CollectiveLaborAgreement { get; set; } = null!;
}
