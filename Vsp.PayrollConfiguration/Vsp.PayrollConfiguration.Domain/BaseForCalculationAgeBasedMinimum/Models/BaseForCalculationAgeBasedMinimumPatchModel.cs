using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationAgeBasedMinimum.Models;

public class BaseForCalculationAgeBasedMinimumPatchModel: IPatchModel
{
    [JsonIgnore]
    public Guid Id { get; set; }

    [Required]
    [Range(0, 999999.99, ErrorMessage = "'{0}' must be between {1} and {2}")]
    public decimal? Minimum { get; set; }
}