using System.Runtime.CompilerServices;
using Vsp.PayrollConfiguration.Infrastructure.Interfaces.Patch;

namespace Vsp.PayrollConfiguration.Domain.Shared.Extensions;

public static class IPatchModelExtensions
{
    private static readonly ConditionalWeakTable<IPatchModel, ValidationContinuation> ValidationContinuations = new();

    public static bool ShouldContinueValidation(this IPatchModel patchModel) 
        => ValidationContinuations.GetValue(patchModel, p => new ValidationContinuation()).ShouldContinue;
    
    public static void SetValidationContinuation(this IPatchModel patchModel, bool shouldContinueValidation) 
        => ValidationContinuations.AddOrUpdate(patchModel, new ValidationContinuation(){ShouldContinue = patchModel.ShouldContinueValidation() && shouldContinueValidation});
}

public class ValidationContinuation
{
    public bool ShouldContinue { get; set; } = true;
}