# 2018-08-24

[*]
end_of_line = crlf

[*.xml]
indent_style = space

[*.cs]
# Prefer "var" everywhere
csharp_style_var_for_built_in_types                   = true : suggestion
csharp_style_var_when_type_is_apparent                = true : suggestion
csharp_style_var_elsewhere                            = true : suggestion

# Prefer method-like constructs to have a block body
csharp_style_expression_bodied_methods                = true:suggestion
csharp_style_expression_bodied_constructors           = true:suggestion
csharp_style_expression_bodied_operators              = true:suggestion

# Prefer property-like constructs to have an expression-body
csharp_style_expression_bodied_properties             = true:suggestion
csharp_style_expression_bodied_indexers               = true:suggestion
csharp_style_expression_bodied_accessors              = true:suggestion

# Suggest more modern language features when available
csharp_style_pattern_matching_over_is_with_cast_check = true : suggestion
csharp_style_pattern_matching_over_as_with_null_check = true : suggestion
csharp_style_inlined_variable_declaration             = true : suggestion
csharp_style_throw_expression                         = true:suggestion
csharp_style_conditional_delegate_call                = true : suggestion

# Newline settings
csharp_new_line_before_open_brace                     = all
csharp_new_line_before_else                           = true
csharp_new_line_before_catch                          = true
csharp_new_line_before_finally                        = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types     = true

# Dotnet code style settings:
[*.{cs, vb}]
# Sort using and Import directives with System.* appearing first
dotnet_sort_system_directives_first                        = true
# Avoid "this." and "Me." if not necessary
dotnet_style_qualification_for_field                       = true : suggestion
dotnet_style_qualification_for_property                    = true : suggestion
dotnet_style_qualification_for_method                      = false : suggestion
dotnet_style_qualification_for_event                       = false : suggestion

# Use language keywords instead of framework type names for type references
dotnet_style_predefined_type_for_locals_parameters_members = true : suggestion
dotnet_style_predefined_type_for_member_access             = true : suggestion

# Suggest more modern language features when available
dotnet_style_object_initializer                            = true : suggestion
dotnet_style_collection_initializer                        = true : suggestion
dotnet_style_coalesce_expression                           = true : suggestion
dotnet_style_null_propagation                              = true : suggestion
dotnet_style_explicit_tuple_names                          = true : suggestion

# EF1001: Internal EF Core API usage.
dotnet_diagnostic.EF1001.severity = warning

# CA1822: Member does not access instance data and can be marked as static
dotnet_diagnostic.CA1822.severity = none

# CA2007: Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2007.severity = none
csharp_style_namespace_declarations= file_scoped:error
csharp_indent_labels = one_less_than_current
csharp_using_directive_placement = outside_namespace:silent
csharp_prefer_simple_using_statement = true:suggestion
csharp_prefer_braces = true:silent
csharp_style_prefer_method_group_conversion = true:silent
csharp_style_prefer_top_level_statements = true:silent
csharp_style_expression_bodied_lambdas = true:silent
csharp_style_expression_bodied_local_functions = true:silent
csharp_style_prefer_null_check_over_type_check = true:suggestion
csharp_prefer_simple_default_expression = true:suggestion
csharp_style_prefer_primary_constructors = true:suggestion
csharp_prefer_system_threading_lock = true:suggestion

[*.{cs,vb}]
#### Naming styles ####

# Naming rules

dotnet_naming_rule.interface_should_be_begins_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_begins_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_begins_with_i.style = begins_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications

dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Naming styles

dotnet_naming_style.begins_with_i.required_prefix = I
dotnet_naming_style.begins_with_i.required_suffix = 
dotnet_naming_style.begins_with_i.word_separator = 
dotnet_naming_style.begins_with_i.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case
dotnet_style_operator_placement_when_wrapping = beginning_of_line
tab_width = 4
indent_size = 4
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion
dotnet_style_prefer_auto_properties = true:suggestion
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
dotnet_style_prefer_conditional_expression_over_assignment = true:suggestion
dotnet_style_prefer_conditional_expression_over_return = true:suggestion
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_compound_assignment = true:suggestion
dotnet_style_prefer_simplified_interpolation = true:suggestion
dotnet_style_namespace_match_folder = true:suggestion

# Verify
[*.{received,verified}.{json,txt,xml}]
charset = "utf-8-bom"
end_of_line = lf
indent_size = unset
indent_style = unset
insert_final_newline = false
tab_width = unset
trim_trailing_whitespace = false
