using Vsp.PayrollConfiguration.Domain.Shared.Models;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.UnitTest.Shared;

/// <summary>
/// Static test data in VspQA:
/// <para>Reference: <see cref="https://dev.azure.com/loket/Scrum/_wiki/wikis/Scrum.wiki/183/Payroll-Configuration-Unit-and-Cypress-test-strategy"/></para>
/// </summary>
public static class SharedConstants
{
    public static class CollectiveLaborAgreements
    {
        public static readonly Guid QA_PayrollConfiguration_CLA_ForLevel_CLA = Guid.Parse("c59445b9-6c03-4423-b405-94e89f250895");
        public static readonly Guid QA_PayrollConfiguration1_CLA_ForLevel_WM = Guid.Parse("8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf");
        public static readonly Guid QA_PayrollConfiguration1_CLA_ForLevel_PA = Guid.Parse("dfdaa0a5-bce8-4ddd-8e1c-42554334c342");

        public static readonly Guid QA_UnitPercentage_CSharp_CLA = Guid.Parse("148b924e-4b79-475c-be39-7c799eb79282");
    }

    public static class WageModels
    {
        /// <summary>
        /// Parent is <see cref="CollectiveLaborAgreements.QA_PayrollConfiguration1_CLA_ForLevel_WM"/>
        /// </summary>
        public static readonly Guid QA_PayrollConfiguration1_WM_ForLevel_WM = Guid.Parse("a77d7e45-2612-450a-86d0-485baecee6ad");

        /// <summary>
        /// Parent is <see cref="CollectiveLaborAgreements.QA_PayrollConfiguration1_CLA_ForLevel_PA"/>
        /// </summary>
        public static readonly Guid QA_PayrollConfiguration1_WM_ForLevel_PA = Guid.Parse("5e010bed-611d-469b-a220-5253e3896f47");
    }

    public static class PayrollAdministrations
    {
        /// <summary>
        /// Parent is <see cref="WageModels.QA_PayrollConfiguration1_WM_ForLevel_PA"/>
        /// </summary>
        public static readonly Guid QA_PayrollConfiguration1_PA_ForLevel_PA = Guid.Parse("bbe616a2-4e59-4d39-8165-ee37a9708be9");
    }

    public static class InheritanceLevels
    {
        public static readonly InheritanceLevelTypeModel CollectiveLaborAgreement = new()
        {
            Key = InheritanceLevel.CollectiveLaborAgreement,
            Value = InheritanceLevel.CollectiveLaborAgreement.ToString()
        };

        public static readonly InheritanceLevelTypeModel WageModel = new()
        {
            Key = InheritanceLevel.WageModel,
            Value = InheritanceLevel.WageModel.ToString()
        };

        public static readonly InheritanceLevelTypeModel PayrollAdministration = new()
        {
            Key = InheritanceLevel.PayrollAdministration,
            Value = InheritanceLevel.PayrollAdministration.ToString()
        };
    }
}
